import { UndoIcon, RedoIcon } from '../../../../assets/icons/icons';
import ToolItem from './tools/ToolItem';
import FormToolButton from './tools/FormToolButton';
import Draggable from './tools/Draggable';
import { ToolList } from './tools/ToolList';

const FormTools = (props: {canUndoButton: boolean, canRedoButton: boolean, undoAction: any, redoAction:any}) => {
	return (
		<div className="">
			<div className="flex justify-evenly">
				<FormToolButton label="Undo" icon={<UndoIcon />} isActive={props.canUndoButton} handleClick={props.undoAction} />
				<FormToolButton label="Redo" icon={<RedoIcon />} isActive={props.canRedoButton} handleClick={props.redoAction} />
			</div>
			<h1 className="text-center text-lg">Tools</h1>
			{ToolList.map(tool => {
				return (
					<Draggable key={tool.id} tool={tool}>
						<ToolItem label={tool.label} icon={tool.icon} />
					</Draggable>)}
				)
			}
		</div>
	);
};

export default FormTools;