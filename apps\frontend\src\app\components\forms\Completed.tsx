import decodeJWT from '../../../utils/jwtdecoder';
import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import SubMenuInnerNavBar from '../common/SubMenuInnerNavBar';
import Table, { ColumnDef } from '../common/Table';
import { getFeatures } from '../../../api/users/userapis';
import { getFormCategories } from '../../../api/forms/formcategoryapis';
import { getSubmittedForms } from '../../../api/forms/formapis';
import Loader from '../common/Loader';
import { useNavigate, useParams } from 'react-router-dom';

const CompletedForms = () => {
  const params = useParams();
  const [tableCount, setTableCount] = useState();
  const [categoryFilter, setCategoryFilter] = useState('All');
  const [filteredCompletedForms, setFilteredCompletedForms] = useState([]);
  const [groupedFormData, setGroupedFormData] = useState<any>({});

  const {status: completedStatus, data: completedForms, isLoading } = useQuery({
    queryKey: ['completed-forms'],
    queryFn: () => getSubmittedForms(),
    refetchOnWindowFocus: true,
  });
  const { data: features } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });
  const { data: formCategories, isLoading: formCategoriesLoading } = useQuery({
    queryKey: ['formCategories'],
    queryFn: () => getFormCategories(),
    refetchOnWindowFocus: false,
  });

  const navigate = useNavigate();
  
  useEffect(() => {
    if (completedForms?.data.length > 0) {
      categoryFilter === 'All'
      ? setFilteredCompletedForms(completedForms?.data)
      : setFilteredCompletedForms(completedForms?.data.filter((f: any) => f.category === categoryFilter));
    }
  }, [categoryFilter, completedForms?.data]);
  
  const completedColumns: ColumnDef[] = [
    {
      key: 'document',
      label: 'Document Name',
      type: 'text',
      truncate: false,
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 cursor-pointer"
          title="Open Form"
          data-tooltip-target="tooltip-default"
            onClick={async () => {
              navigate(
                params?.mineId
                ? `/app/Mines/${params?.mineId}/Form/${row.formId}`
                : `/app/Form/${row.formId}`
              );
            }}>
          <u>{row.document}</u>
        </div>
      ),
    },
    // {
    //   key: 'name',
    //   label: 'Form Name',
    //   type: 'text',
    //   render: (row: any) => (
    //     <div className="text-ellipsis overflow-hidden w-35">
    //       {row.name}
    //     </div>
    //   ),
    // },
    // {
    //   key: 'category',
    //   label: 'Category',
    //   type: 'text',
    //   render: (row: any) => (
    //     <div className="text-ellipsis overflow-hidden w-35 ">
    //       {row.category}
    //     </div>
    //   ),
    // },
    {
      key: 'shift',
      label: 'Shift',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.shift && row.shift.length > 0 ? row.shift : '-'}
        </div>
      ),
    },
    {
      key: 'section',
      label: 'Section',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.section && row.section.length > 0 ? row.section : '-'}
        </div>
      ),
    },
    {
      key: 'date',
      label: 'Date',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.date && row.date.length > 0 ? row.date : '-'}
        </div>
      )
    },
    // {
    //   key: 'version',
    //   label: 'Version',
    //   type: 'text',
    //   render: (row: any) => (
    //     <div className="text-ellipsis overflow-hidden w-35 ">
    //       {`${row.major}.${row.minor}.${row.revision}`}
    //     </div>
    //   ),
    // },
    {
      key: 'submittedBy',
      label: 'Completed By',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row?.submittedByUser ?? row?.updatedByUser ?? row?.createdByUser ?? '-'}
        </div>
      ),
    },
  ];

  if ((features?.data.some((feature: any) => feature.FeatureName == 'CompletedForms') ||
    decodeJWT()?.role == 'superuser') &&
    formCategories?.data.length > 0
  ) {
    const catArr = formCategories?.data.map((c:any) => c?.name);
    const formCategoryList:string[] = ['All',...catArr];
    return (
      <>
        <SubMenuInnerNavBar
          leftTitle="Completed"
          rightTitle="Forms"
          tabNames={formCategoryList}
          showSearchBox={false}
          pathToRender={
            decodeJWT()?.role == 'superuser'
              ? `Mines/${params?.mineId}/Forms/Completed`
              : 'Forms/Completed'
          }
          setCategoryFilter={setCategoryFilter}
          defaultTab='All'
        />
        <div className="mt-8 px-10 2xl:px-16">
          <div>
            {isLoading ? (
              <div>
                <div>
                  <div className="flex justify-center items-center h-full pt-[200px] white">
                    {<Loader />}
                  </div>
                  <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                    Loading....
                  </div>
                </div>
              </div>
            ) : (
              <div className={`pt-8 userMTableBg px-5 rounded-xl
                ${filteredCompletedForms.length <= 0 ? 'pb-8' : 'pb-2'}`}>
                <div className="flex justify-between">
                  <div className="block">
                    <h6 className="font-semibold text-[20px] text-white">
                      {`Completed ${categoryFilter == 'All' ? '' : categoryFilter} Forms`}
                    </h6>
                  </div>
                </div>
                <div className="mt-5">
                  {filteredCompletedForms.length > 0 ? (
                    <div>
                      <Table
                        columns={completedColumns}
                        data={
                          filteredCompletedForms?.map((ele: any) => ({
                            document:
                              ele?.document,
                            ...ele,
                          })) ?? []
                        }
                        searchText=''
                        sortable={false}
                        searchOnColumn={'document'}
                        separateLine={false}
                        scrollable={true}
                        dataRenderLimitMdScreen={8}
                        dataRenderLimitLgScreen={15}
                        tableHeightClassLg="tableheightForlg"
                        tableHeightClassMd="tableheightFormd"
                        setTableCount={setTableCount}
                      />
                    </div>
                  ) : (
                    <div className="text-[26px] font-bold text-center text-white">
                      {`There are currently no completed ${categoryFilter == 'All' ? '' : categoryFilter} forms`}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </>
    );
  } else {
    return '';
  }
};

export default CompletedForms;
