import { Module } from '@nestjs/common';
import { CompanyOnboardController } from './company_onboard.controller';
import { CompanyOnboardService } from './company_onboard.service';
import { CompaniesModule } from '../companies/companies.module';
import { MinesModule } from '../mines/mines.module';
import { UsersModule } from '../users/users.module';
import { RolesModule } from '../roles/roles.module';

@Module({
  imports: [CompaniesModule, MinesModule, UsersModule, RolesModule],
  controllers: [CompanyOnboardController],
  providers: [CompanyOnboardService],
})
export class CompanyOnboardModule {}
