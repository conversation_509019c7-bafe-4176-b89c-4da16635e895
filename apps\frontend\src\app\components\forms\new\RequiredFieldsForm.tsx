import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { getActiveShifts } from '../../../../api/shifts/shiftapis';
import { getSections } from '../../../../api/sections/sectionapis';
import { useQuery } from '@tanstack/react-query';
import { CloseIcon } from '../../../../assets/icons/icons';
import { DefaultFormData } from '../types';

const schema = yup.object({
  date: yup.string().required('Please enter a Shift Date'),
  shift: yup.string().required('Please select a Shift'),
  section: yup.string().required('Please select a Section'),
});

interface Props {
  shifts: any,
  sections: any,
	setDefaultFormData: (data: DefaultFormData | null) => void;
  setOpenNewFormRequiredFields: (value: boolean) => void;
}

export default function AddFormTemplateForm({
  shifts,
  sections,
  setDefaultFormData,
  setOpenNewFormRequiredFields,
}: Props) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    getValues,
    setError,
    clearErrors,
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmitHandler = async (data: any) => {
    let selectedShift, selectedSection, selectedDate;
    if(shifts && data?.shift) {
      let shiftIndex = shifts.findIndex((s:any) => s.id = data?.shift);
      selectedShift = shifts[shiftIndex];
    }
    if(sections && data?.section) {
      let sectionIndex = sections.findIndex((s:any) => s.id = data?.section);
      selectedSection = sections[sectionIndex];
    }
    if(data?.date) {
      selectedDate = data.date;
    }
    
    setOpenNewFormRequiredFields(false);
    setDefaultFormData({
      selectedShift,
      selectedSection,
      selectedDate,
      continue: true
    });
  };

  useEffect(() => {
    let now = new Date();
    setValue('date', now.toISOString().slice(0, 10));
    if(shifts.length > 0) {
      clearErrors();
      for(let i = 0; i < shifts.length; i++) {
        let s = shifts[i];
        let startTime = new Date(s.startTime).toTimeString();
        let endTime = new Date(s.endTime).toTimeString();
        let nowTime = now.toTimeString();
        
        if(endTime > startTime && nowTime > startTime && endTime > nowTime) {
          setValue('shift', s.id);
        }
        if(startTime > endTime && (nowTime > startTime || (nowTime < startTime && nowTime < endTime))) {
          setValue('shift', s.id);
        }
      }
    }
  }, [shifts]);

  return (
    <div>
      <div className="">
        <div className="flex justify-between mx-4">
          <div className="block">
            <h6 className="font-semibold text-[24px] text-white">
              Required Fields
            </h6>
            <p className="font-normal my-1 text-[16px] tracking-normal leading-5 text-[#cccccc] ">
              Enter required fields
            </p>
          </div>
          <div
            className="mt-1 cursor-pointer"
            onClick={() => {
              setOpenNewFormRequiredFields(false);
            }}
          >
            <CloseIcon />
          </div>
        </div>
        <div className="">
          <form onSubmit={handleSubmit(onSubmitHandler)}>
            <div className="rounded pt-3">
              <div className="m-2">
                <div className="grid grid-cols-1">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Date
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('date')}
                      type="date"
                      name="date"
                      id="dateStr"
                      className="bg-gray-200 p-1.5 w-full rounded pl-2 text-[14px] text-black"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.date?.message}
                    </p>
                  </div>
                  <div></div>
                </div>

                <div className="grid grid-cols-1">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Shift
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <select
                      {...register('shift')}
                      name="shift"
                      id="shift"
                      onChange={() => {
                        clearErrors('shift');
                      }}
                      className={'block w-full p-[7px] rounded bg-gray-200 focus:border-blue-500 text-[14px] text-black'}
                    >
                      <option className="hidden" value={''}>
                        Select Shift
                      </option>
                      {shifts.map((ele: any) => (
                        <option key={ele?.id} value={ele?.id} className="text-black">
                          {ele.shiftName.charAt(0).toUpperCase() + ele.shiftName.slice(1)}
                        </option>
                      ))}
                    </select>
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.shift?.message}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Section
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <select
                      {...register('section')}
                      name="section"
                      id="section"
                      onChange={() => {
                        clearErrors('section');
                      }}
                      className={'block w-full p-[7px] rounded bg-gray-200 focus:border-blue-500 text-[14px] text-black'}
                    >
                      <option className="hidden" value={''}>
                        Select Section
                      </option>
                      {sections.map((ele: any) => (
                        <option key={ele?.id} value={ele?.id} className="text-black">
                          {ele.sectionName.charAt(0).toUpperCase() + ele.sectionName.slice(1)}
                        </option>
                      ))}
                    </select>
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.section?.message}
                    </p>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 mt-5">
                <div className="mx-3">
                  <button
                    id="cancel_button"
                    title="Click to cancel"
                    onClick={() => {
                      setOpenNewFormRequiredFields(false);
                    }}
                    className={`
                      text-white bg-transparent border-[#4AA8FE] border-[1px] hover:border-[#4AA8FE]/75
                      font-medium rounded-lg text-sm px-4 py-2 text-center h-9 w-full items-center me-2 mb-2
                    `}
                  >
                    {'Cancel'}
                  </button>
                </div>
                <div className="mx-3">
                  <button
                    id="create_form_template"
                    title="Click to save required fields"
                    type="submit"
                    className={`
                      text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 font-medium rounded-lg
                      text-sm px-4 py-2 text-center h-9 w-full items-center me-2 mb-2
                    `}
                  >
                    Continue
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
