import { Injectable } from '@nestjs/common';
import { OnBoardCompanyDto } from './dto/onboard-compnay.dto';
import { CompaniesService } from '../companies/companies.service';
import { MinesService } from '../mines/mines.service';
import { UsersService } from '../users/users.service';
import { CreateCompanyDto } from '../companies/dto/create-company.dto';
import { CreateMineDto } from '../mines/dtos/create-mine.dto';
import { CreateUserDto } from '../users/dto/create-user.dto';
import { RolesService } from '../roles/roles.service';

@Injectable()
export class CompanyOnboardService {
  constructor(
    private companyService: CompaniesService,
    private mineService: MinesService,
    private userService: UsersService,
    private roleService: RolesService
  ) {}
  async onBoardCompany(onboardCompanyDto: OnBoardCompanyDto, user: any) {
    const companyDto = new CreateCompanyDto();
    companyDto.name = companyDto.code = onboardCompanyDto.companyName;
    companyDto.createdBy = onboardCompanyDto.createdBy;
    const company = await this.companyService.create(companyDto);

    const mineDto = new CreateMineDto();
    mineDto.name = onboardCompanyDto.mineName;
    mineDto.code = onboardCompanyDto.mineCode;
    mineDto.location = onboardCompanyDto.mineLocation;
    mineDto.companyId = company.id;
    mineDto.timezoneId = onboardCompanyDto?.timezone;
    const mine = await this.mineService.create(mineDto, user);

    const companyRole = await this.roleService.findAll(company.id);
    const adminRole = companyRole.find((role) => role.name == 'admin');

    if (onboardCompanyDto.email && onboardCompanyDto.username) {
      const userDto = new CreateUserDto();
      userDto.firstName = onboardCompanyDto.firstName;
      userDto.lastName = onboardCompanyDto.lastName;
      userDto.username = onboardCompanyDto.username;
      userDto.email = onboardCompanyDto.email;
      userDto.companyId = company.id;
      userDto.roleId = adminRole.id;
      userDto.mineId = mine.id;
      await this.userService.createUser(userDto, user);
    }

    onboardCompanyDto.companyId = company.id;
    return onboardCompanyDto;
  }
}
