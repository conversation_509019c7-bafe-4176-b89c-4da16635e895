import { Params, useParams } from 'react-router-dom';
import { object } from 'yup';

const pageNameMapping = [
  { pageName: 'LocationDashboard', url: 'Location/dashboard' },
  { pageName: 'LocationLiveCheckins', url: 'Location/live/checkins' },
  { pageName: 'LocationLiveSection', url: 'Location/live/sections' },
  { pageName: 'LocationLiveWatchlist', url: 'Location/live/watchlist' },
  { pageName: 'LocationReportCheckins', url: 'Location/report/checkins' },
  { pageName: 'LocationReportSection', url: 'Location/report/sections' },
  { pageName: 'LocationReportPersonnel', url: 'Location/report/personnel' },
  { pageName: 'ProductionLiveMine', url: 'Production/live/mine' },
  { pageName: 'ProductionLiveSections', url: 'Production/live/sections' },
  { pageName: 'ProductionLiveAlerts', url: 'Production/live/alerts' },
  { pageName: 'ProductionReportMine', url: 'Production/report/mine' },
  { pageName: 'ProductionReportSection', url: 'Production/report/sections' },
  { pageName: 'ProductionReportComapre', url: 'Production/report/compare' },
  { pageName: 'Compliance', url: '/Compliance' },
  { pageName: 'Users', url: 'Setting/users' },
  { pageName: 'Features', url: 'Setting/Features' },
  { pageName: 'AuditLogs', url: '/AuditLogs' },
  { pageName: 'Mines', url: 'Mines' },
  { pageName: 'Goals', url: 'Setting/Goals' },
  { pageName: 'Templates', url: 'Forms/Templates' },
  { pageName: 'New', url: 'Forms/New' },
  { pageName: 'Complete', url: 'Forms/Completed' },
  { pageName: 'Designer', url: 'Forms/Designer' },
  { pageName: 'NewForm', url: 'Form/New' },
  { pageName: 'New', url: 'Form' },
  { pageName: 'Shifts', url: 'Setting/Shifts' },
];

export function getPageNamesFromUrl(requestedUrl: string) {
  const normalizeUrl = (url: string) =>
    url.replace(/\d+/g, '').toLowerCase().replace(/\/$/, '');

  const normalizedUrl = normalizeUrl(requestedUrl);
  const matchedPages = pageNameMapping
    .filter(({ url }) => normalizedUrl.includes(normalizeUrl(url))) 
    .sort((a, b) => b.url.length - a.url.length) 
    .map(({ pageName }) => pageName);
  return matchedPages.length > 0 ? matchedPages[0] : 'unknownpage';
}
