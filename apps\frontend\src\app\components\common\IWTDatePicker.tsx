import Datepicker from 'react-tailwindcss-datepicker';
import { useState, useEffect, useRef } from 'react';
import decodeJWT from '../../../utils/jwtdecoder';
import dayjs from 'dayjs';
import { useQuery } from '@tanstack/react-query';
import { useSaveDateRange } from '../../../services/mutations/usermutations';
import { getDateRangerForUser } from '../../../api/users/dateSelectionapis';
import { useParams } from 'react-router-dom';
import { shortcuts, prodReportShortcuts } from '../../../utils/constant';
import { toast } from 'react-toastify';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { endOfDay } from 'date-fns';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';

const IWTDatePicker = (props: any) => {
  const today = dayjs();
  const startOfWeek = today.subtract(6, 'day');
  const endOfWeek = today;
  const wrapperRef = useRef(null);

  const [timeZone, setTimeZone] = useState('');

  useEffect(() => {
    const browserTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    setTimeZone(browserTimeZone);
  }, []);

  const browserTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  dayjs.extend(utc);
  dayjs.extend(timezone);
  dayjs.extend(isSameOrBefore);

  const getLastMonthToPast30Days = () => {
    const today = new Date();
    const startDate = new Date(today);
    startDate.setMonth(today.getMonth() - 1);
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() - 31);

    return { startDate, endDate };
  };

  const comareFromDate = getLastMonthToPast30Days();

  const getLast30Days = () => {
    const today = new Date();
    const startDate = new Date(today);
    startDate.setMonth(today.getMonth() - 1);
    const endDate = new Date(today);
    return { startDate, endDate };
  };

  const comareToDate = getLast30Days();

  const [dateRangeSelected, setDateRangeSelected] = useState({
    startDate: startOfWeek.format('YYYY-MM-DD'),
    endDate: endOfWeek.format('YYYY-MM-DD'),
  });

  const [prodMineDateSelected, setProdMineDateSelcted] = useState({
    startDate: comareToDate.startDate,
    endDate: comareToDate.endDate,
  });

  const [compareDateFrom, setCompareDateFrom] = useState({
    startDate: comareFromDate.endDate,
    endDate: comareFromDate.startDate,
  });

  const [compareDateTo, setCompareDateTo] = useState({
    startDate: comareToDate.startDate,
    endDate: comareToDate.endDate,
  });

  const datePickerRef = useRef(null);

  const params = useParams();

  const handleValueChange = async (newValue: any) => {
    if (params['*']?.includes('Location/report/personnel')) {
      handleSaveDateRange(newValue, 'location', shortcuts);
    } else if (params['*']?.includes('Production/report/mine')) {
      handleSaveDateRange(newValue, 'production', prodReportShortcuts);
    } else if (params['*']?.includes('Production/report/compare')) {
      const type = props?.firstDatePicker ? 'prodCompareFrom' : 'prodCompareTo';
      handleSaveDateRange(newValue, type, shortcuts);
    } else if (params['*']?.includes('AuditLogs')) {
      props?.setAuditLogDateRange(newValue);
      mixpanel.track('Date Selection', {
        Page_Name: params['*'],
        MshaId: decodeJWT()?.mshaId,
      });
    }
  };

  const handleSaveDateRange = async (
    newValue: any,
    type: string,
    shortcuts: any
  ) => {
    let flag = false;
    for (const [shortcut, range] of Object.entries(shortcuts)) {
      const { start, end } = range?.period;
      if (
        newValue?.startDate === dayjs(start).format('YYYY-MM-DD') &&
        newValue?.endDate === dayjs(end).format('YYYY-MM-DD') &&
        !params['*']?.includes('Production/report/compare')
      ) {
        flag = true;
        let newStartDate = '';
        let newEndDate = '';
        mixpanel.track('Date Selection Shortcut', {
          Page_Name: getPageNamesFromUrl(params['*']),
          MshaId: decodeJWT()?.mshaId,
        });
        if (shortcut !== 'thisMonth' && shortcut !== 'thisweek') {
          newStartDate = dayjs(newValue?.startDate).format('YYYY-MM-DD');
          newEndDate = dayjs(newValue?.endDate).format('YYYY-MM-DD');
        }

        if (shortcut === 'thisMonth') {
          newStartDate = dayjs().startOf('month').format('YYYY-MM-DD');
          newEndDate = dayjs().format('YYYY-MM-DD');
        } else if (shortcut === 'thisweek') {
          newStartDate = dayjs()
            .startOf('week')
            .add(1, 'day')
            .format('YYYY-MM-DD');
          newEndDate = dayjs().format('YYYY-MM-DD');
        }

        newValue = { startDate: newStartDate, endDate: newEndDate };

        if (params['*']?.includes('Location/report/personnel')) {
          sessionStorage.setItem(
            'locationPersonnelRange',
            JSON.stringify(newValue)
          );
          setDateRangeSelected(newValue);
        } else if (params['*']?.includes('Production/report/mine')) {
          sessionStorage.setItem('ProdRepMine', JSON.stringify(newValue));
          setProdMineDateSelcted(newValue);
        }
        break;
      }
    }
    if (!flag) {
      const firstDate = dayjs(newValue?.startDate);
      const secondDate = dayjs(newValue?.endDate);
      const diffDays = secondDate.diff(firstDate, 'day');
      mixpanel.track('Date Selection', {
        Page_Name: getPageNamesFromUrl(params['*']),
        MshaId: decodeJWT()?.mshaId,
      });

      if (diffDays > 30 && params['*']?.includes('Production/report/mine')) {
        toast.error("You can't select a date range of more than 30 days");
        return;
      }

      if (params['*']?.includes('Location/report/personnel')) {
        newValue.startDate !== null &&
          newValue?.endDate !== null &&
          sessionStorage.setItem(
            'locationPersonnelRange',
            JSON.stringify({
              startDate: newValue?.startDate,
              endDate: newValue?.endDate,
            })
          );
        setDateRangeSelected({
          startDate: newValue?.startDate,
          endDate: newValue?.endDate,
        });
      } else if (params['*']?.includes('Production/report/mine')) {
        newValue.startDate !== null &&
          newValue?.endDate !== null &&
          sessionStorage.setItem(
            'ProdRepMine',
            JSON.stringify({
              startDate: newValue?.startDate,
              endDate: newValue?.endDate,
            })
          );
        setProdMineDateSelcted({
          startDate: newValue?.startDate,
          endDate: newValue?.endDate,
        });
      } else if (
        params['*']?.includes('Production/report/compare') &&
        props?.firstDatePicker
      ) {
        newValue.startDate !== null &&
          newValue?.endDate !== null &&
          sessionStorage.setItem(
            'prodCompareFrom',
            JSON.stringify({
              startDate: newValue?.startDate,
              endDate: newValue?.endDate,
            })
          );
        setCompareDateFrom({
          startDate: newValue?.startDate,
          endDate: newValue?.endDate,
        });
      } else if (props?.secondDatePicker) {
        newValue.startDate !== null &&
          newValue?.endDate !== null &&
          sessionStorage.setItem('prodCompareTo', JSON.stringify(newValue));
        setCompareDateTo({
          startDate: newValue?.startDate,
          endDate: newValue?.endDate,
        });
      }
    }
  };

  const selectedPersonnelRange: any = sessionStorage.getItem(
    'locationPersonnelRange'
  );
  const personnelRange = JSON.parse(selectedPersonnelRange);
  const selectedMineRange: any = sessionStorage.getItem('ProdRepMine');
  const mineRange = JSON.parse(selectedMineRange);
  const prodCompareFromRange: any = sessionStorage.getItem('prodCompareFrom');
  const compareFrom = JSON.parse(prodCompareFromRange);
  const prodCompareToRange: any = sessionStorage.getItem('prodCompareTo');
  const compareTo = JSON.parse(prodCompareToRange);

  useEffect(() => {
    if (params['*']?.includes('Location/report/personnel')) {
      if (selectedPersonnelRange) {
        setDateRangeSelected(personnelRange);
      }
      props?.setPersonnelRange(personnelRange);
    }
  }, [
    params['*']?.includes('Location/report/personnel'),
    selectedPersonnelRange,
  ]);

  useEffect(() => {
    if (params['*']?.includes('Production/report/mine')) {
      if (selectedMineRange) {
        setProdMineDateSelcted(mineRange);
      }
      setProdMineDateSelcted(mineRange || getLast30Days());
      props?.setReportMineDate(mineRange || getLast30Days());
    }
  }, [params['*']?.includes('Production/report/mine'), selectedMineRange]);

  useEffect(() => {
    if (
      params['*']?.includes('Production/report/compare') &&
      props?.firstDatePicker === true
    ) {
      if (prodCompareFromRange) {
        setCompareDateFrom(compareFrom);
        props?.setCompareFromDate(compareFrom || getLastMonthToPast30Days);
      } else {
        setCompareDateFrom(compareFrom);
        // props?.setCompareFromDate({
        //   startDate: dayjs(comareFromDate?.endDate).format('YYYY-MM-DD'),
        //   endDate: dayjs(comareFromDate?.endDate).format('YYYY-MM-DD'),
        // });
      }
    }
  }, [prodCompareFromRange]);

  useEffect(() => {
    if (props?.secondDatePicker) {
      if (prodCompareToRange) {
        setCompareDateTo(compareTo);
        props?.setCompareToDate(compareTo || getLast30Days());
      } else {
        setCompareDateTo(compareTo);
        // props?.setCompareToDate({
        //   startDate: dayjs(comareToDate.startDate).format('YYYY-MM-DD'),
        //   endDate: dayjs(comareToDate.endDate).format('YYYY-MM-DD'),
        // });
      }
    }
  }, [prodCompareToRange]);

  useEffect(() => {
    if (datePickerRef.current) {
      const inputElement = datePickerRef?.current?.querySelector('input');
      if (inputElement) {
        inputElement.title = params['*']?.includes('AuditLogs')
          ? `${dayjs(props?.auditLogDateRange?.startDate).format(
              'MMM DD, YYYY'
            )} - ${dayjs(props?.auditLogDateRange?.endDate).format(
              'MMM DD, YYYY'
            )}`
          : params['*']?.includes('Location/report/personnel')
          ? `${dayjs(dateRangeSelected?.startDate).format(
              'MMM DD, YYYY'
            )} - ${dayjs(dateRangeSelected?.endDate).format('MMM DD, YYYY')}`
          : params['*']?.includes('Production/report/mine')
          ? `${dayjs(prodMineDateSelected?.startDate).format(
              'MMM DD, YYYY'
            )} - ${dayjs(prodMineDateSelected?.endDate).format('MMM DD, YYYY')}`
          : params['*']?.includes('Production/report/compare')
          ? props.firstDatePicker
            ? `${dayjs(compareDateFrom?.startDate).format(
                'MMM DD, YYYY'
              )} - ${dayjs(compareDateFrom?.endDate).format('MMM DD, YYYY')}`
            : props?.secondDatePicker
            ? `${dayjs(compareDateTo?.startDate).format(
                'MMM DD, YYYY'
              )} - ${dayjs(compareDateTo?.endDate).format('MMM DD, YYYY')}`
            : ''
          : '';
      }
    }
  }, [
    dateRangeSelected,
    prodMineDateSelected,
    compareDateFrom,
    compareDateTo,
    props?.auditLogDateRange?.startDate,
    props?.auditLogDateRange?.endDate,
    params['*']?.includes('Location/report/personnel'),
    // props,
  ]);

  const startDate =
    params['*']?.includes('Location/report/personnel') && dateRangeSelected
      ? dateRangeSelected?.startDate
      : params['*']?.includes('Production/report/mine') && prodMineDateSelected
      ? prodMineDateSelected?.startDate
      : params['*']?.includes('Production/report/compare') &&
        props?.firstDatePicker &&
        compareDateFrom
      ? comareFromDate?.startDate
      : params['*']?.includes('Production/report/compare') &&
        props?.secondDatePicker &&
        compareDateTo
      ? compareDateTo?.startDate
      : null;

  const handleCalendarIconClick = () => {
    console.log('handleCalendarIconClick');
    const input = wrapperRef.current?.querySelector('input');
    if (input) input.focus();
  };

  return (
    <div className="custom-datepicker relative " ref={wrapperRef}>
      <Datepicker
        value={
          params['*']?.includes('AuditLogs')
            ? props?.auditLogDateRange
            : params['*']?.includes('Location/report/personnel')
            ? dateRangeSelected
            : params['*']?.includes('Production/report/mine')
            ? prodMineDateSelected
            : params['*']?.includes('Production/report/compare') &&
              props?.firstDatePicker
            ? compareDateFrom
            : props?.secondDatePicker
            ? compareDateTo
            : dateRangeSelected
        }
        onChange={handleValueChange}
        asSingle={props?.asSingle}
        displayFormat="MMM DD, YYYY"
        separator={'-'}
        showShortcuts={props?.showShortcuts}
        className={'custom-datepicker'}
        popoverDirection="down"
        useRange={false}
        maxDate={new Date().setHours(0, 0, 0, 0)}
        configs={{
          shortcuts: params['*']?.includes('Production/report/mine')
            ? prodReportShortcuts
            : shortcuts,
        }}
        startFrom={startDate}
      />

      <div
        className="absolute bottom-[10px] right-[10px] cursor-pointer peer"
        onClick={handleCalendarIconClick}
        title=" Edit date range"
      >
        <svg
          className="h-5 w-5 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth="1.5"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5m-9-6h.008v.008H12v-.008zM12 15h.008v.008H12V15zm0 2.25h.008v.008H12v-.008zM9.75 15h.008v.008H9.75V15zm0 2.25h.008v.008H9.75v-.008zM7.5 15h.008v.008H7.5V15zm0 2.25h.008v.008H7.5v-.008zm6.75-4.5h.008v.008h-.008v-.008zm0 2.25h.008v.008h-.008V15zm0 2.25h.008v.008h-.008v-.008zm2.25-4.5h.008v.008H16.5v-.008zm0 2.25h.008v.008H16.5V15z"
          />
        </svg>
      </div>
    </div>
  );
};

export default IWTDatePicker;
