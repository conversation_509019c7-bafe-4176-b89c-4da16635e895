import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  UseInterceptors,
  UploadedFile,
  ParseFilePipeBuilder,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FormTemplate } from './entities/form_template.entity';
import { FormTemplatesService } from './form_templates.service';
import { CreateFormTemplateDto } from './dto/create-form-template.dto';
import { UpdateFormTemplateDto } from './dto/update-form-template.dto';
import { ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { Roles } from '../auth/gurads/roles.decorator';
import { RolesGuard } from '../auth/gurads/roles.guard';

@Controller('portal/v1/formTemplates')
@ApiTags('formTemplates')
export class FormTemplatesController {
  constructor(private readonly formTemplatesService: FormTemplatesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(@Body() createFormTemplateDto: CreateFormTemplateDto, @Req() req) {
    return this.formTemplatesService.create(createFormTemplateDto, req.user);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(@Req() req) {
    let mineId = req.user.mineid;
    return this.formTemplatesService.findAll(mineId);
  }

  @Get('history/:id')
  @UseGuards(JwtAuthGuard)
  async findTemplateHistory(@Param('id') id: number) {
    return this.formTemplatesService.findTemplateHistory(id);
  }

  @Get('published')
  @UseGuards(JwtAuthGuard)
  async findAllPublished(@Req() req) {
    let mineId = req.user.mineid;
    return this.formTemplatesService.findAllPublished(mineId);
  }

  @Get('name/:name')
  @UseGuards(JwtAuthGuard)
  async findByName(@Param('name') name: string): Promise<FormTemplate> {
    const decodedName = decodeURIComponent(name);
    return await this.formTemplatesService.findByName(decodedName);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async findOne(@Param('id') id: number) {
    return this.formTemplatesService.findOne(id);
  }

  @Get('latest-revision/:templateId')
  @UseGuards(JwtAuthGuard)
  async findLatestRevision(@Param('templateId') templateId: number) {
    return this.formTemplatesService.findLatestRevision(templateId);
  }

  @Get('revision/:definitionId')
  @UseGuards(JwtAuthGuard)
  async findRevision(@Param('definitionId') definitionId: number) {
    return this.formTemplatesService.findRevision(definitionId);
  }

  @Get('published-revision/:templateId')
  @UseGuards(JwtAuthGuard)
  async findPublishedRevision(@Param('templateId') templateId: number) {
    return this.formTemplatesService.findPublishedRevision(templateId);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  async update(@Param('id') id: number, @Body() updateFormTemplateDto: UpdateFormTemplateDto, @Req() req) {
    return this.formTemplatesService.update(id, updateFormTemplateDto, req.user);
  }

  @Post('clone/:id')
  @UseGuards(JwtAuthGuard)
  async clone(@Param('id') id: number, @Body() createFormTemplateDto: CreateFormTemplateDto, @Req() req) {
    return this.formTemplatesService.clone(id, createFormTemplateDto, req.user);
  }

  @Patch('isDelete/:id')
  @Roles(['admin', 'superuser'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  async deleteTemplate(@Param('id') id: number, @Req() req) {
    let deletedUser = await this.formTemplatesService.deleteTemplate(id, req.user);
    return deletedUser;
  }

  @Post('uploadFile')
  @UseInterceptors(FileInterceptor('file'))
  @UseGuards(JwtAuthGuard)
  async uploadFile(@UploadedFile(
    new ParseFilePipeBuilder()
      .addMaxSizeValidator({
        maxSize: 5000000
      })
      .build()
  ) file: Express.Multer.File) {
    return this.formTemplatesService.uploadFile(file);
  }
}
