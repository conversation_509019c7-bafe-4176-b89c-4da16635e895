import { ApiClient } from '../apiClient';

export interface FormTemplateData {
  id?: number;
  name?: string;
  description?: string;
  categoryId?: number;
}

export interface FormTemplateDefinitionData {
  id?: number;
  templateId?: number;
  definition?: string;
}

export const checkTemplateNameMutation = async (name: string) => {
  const encodedName = encodeURIComponent(name);
  return await ApiClient.get(`/api/portal/v1/formTemplates/name/${encodedName}`);
}

export const addFormTemplate = async (data: FormTemplateData) =>
  await ApiClient.post(`/api/portal/v1/formTemplates`, data);

export const getFormTemplates = async () =>
  await ApiClient.get(`/api/portal/v1/formTemplates`);

export const getFormTemplateHistory = async (id: any) =>
  await ApiClient.get(`/api/portal/v1/formTemplates/history/${id}`);

export const getPublishedForms = async () => {
  return await ApiClient.get(`/api/portal/v1/formTemplates/published`);
}

export const getFormTemplate = async (id: any) =>
  await ApiClient.get(`/api/portal/v1/formTemplates/${id}`);

export const getLatestRevision = async (id: any) =>
  await ApiClient.get(`/api/portal/v1/formTemplates/latest-revision/${id}`);

export const getRevision = async (id: any) =>
  await ApiClient.get(`/api/portal/v1/formTemplates/revision/${id}`);

export const getPublishedRevision = async (id: any) =>
  await ApiClient.get(`/api/portal/v1/formTemplates/published-revision/${id}`);

export const editFormTemplate = async (id: number, data: any) => {
  return await ApiClient.patch(`/api/portal/v1/formTemplates/${id}`, data);
}

export const cloneFormTemplate = async (id: number, data: any) => {
  return await ApiClient.post(`/api/portal/v1/formTemplates/clone/${id}`, data);
}

export const getFormDefinition = async (id: any) =>
  await ApiClient.get(`/api/portal/v1/formTemplateDefinitions/${id}`);

export const addDefinitionRevision = async (id: number, data: FormTemplateDefinitionData) => {
  return await ApiClient.post(`/api/portal/v1/formTemplateDefinitions/revise/${id}`, data);
}

export const editFormTemplateDefinition = async (id: number, data: FormTemplateDefinitionData) => {
  return await ApiClient.patch(`/api/portal/v1/formTemplateDefinitions/${id}`, data);
}

export const publishFormTemplateDefinition = async (id: number) => {
  return await ApiClient.patch(`/api/portal/v1/formTemplateDefinitions/publish/${id}`);
}

export const deleteFormTemplate = async (id: number) =>
  await ApiClient.patch(`/api/portal/v1/formTemplates/isDelete/${id}`);

export const deleteFormTemplateDefinition = async (id: number) =>
  await ApiClient.patch(`/api/portal/v1/formTemplateDefinitions/isDelete/${id}`);

export const uploadFile = async (data: FormData) =>
  await ApiClient.post(`/api/portal/v1/formTemplates/uploadFile`, data, {headers: { 'content-type': 'multipart/form-data' }});
