const Card = ({
  title,
  subtitle,
  svgIcon,
  type,
}: {
  title: number;
  subtitle: string;
  svgIcon: any;
  type:string;
}) => {
  const iconType = svgIcon?.type?.name;
  console.log('svgIcon type name:', iconType);
  return (
    // <div className=" grid grid-rows-3 gap-1 rounded-[8px] border-[1px] border-[#4AA8FE]/50 p-1 px-2 text-white">
    //   <div className=" row-span-2 flex text-center gap-2">
    //     <div className="flex items-center gap-2 mt-2">{svgIcon}</div>
    //     <span className="font-[700]  text-[36px] text-ellipsis overflow-hidden">
    //       {title}
    //     </span>
    //   </div>
    //   <span className=" font-[400] text-[12px] pl-5">{subtitle}</span>
    // </div>
    <div className="flex flex-col items-center py-2 rounded-[8px] border border-[#4AA8FE]/50 p-2 text-white w-[125px]">
      <div className="flex items-center space-x-1 mb-2 whitespace-nowrap self-start">
        {svgIcon}
          <span className="text-[#ffb132] text-[12px] p-1">
            {type}
          </span>
      </div>
    <div className="font-[700] text-[36px] leading-none">{title}</div>
    <div className="font-[400] text-[12px]">{subtitle}</div>
  </div>
    // <div className="grid grid-cols-5 py-2 rounded-[8px] border-[1px] border-[#4AA8FE]/50 p-1 px-2 text-white">
    //   <div className="col-span-1 flex justify-end ">{svgIcon}</div>
    //   <div className="col-span-4  pr-1 w-full text-center flex flex-col  ">
    //     <div className="font-[700] text-[36px] text-ellipsis">{title}</div>
    //     <div className="font-[400] text-[12px]">{subtitle}</div>
    //   </div>
    // </div>
    
  );
};

export default Card;
