import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  useAddShift,
  useEditShift,
  useCheckShiftNameMutation
} from '../../../services/mutations/shiftmutations';
import { useQuery } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import decodeJWT from '../../../utils/jwtdecoder';
import { CloseIcon } from '../../../assets/icons/icons';
import { getPageNamesFromUrl } from '../PageName';
import { useParams } from 'react-router-dom';

const schema = yup.object({
  shiftName: yup
    .string()
    .max(100, 'Name should not exceed 100 characters')
    .required('Please enter a shift name'),
  startTime: yup
    .string()
    .required('Please enter a shift start time'),
  endTime: yup
    .string()
    .required('Please enter a shift end time'),
  shift_type: yup.string().required('Please select a shift type'),
});

interface Props {
	editData: any;
	setOpenAddShiftForm: (value: boolean) => void;
}

export default function AddShiftForm({ editData, setOpenAddShiftForm }: Props) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    getValues,
    setError,
    clearErrors,
  } = useForm({
    resolver: yupResolver(schema),
  });

  const addShiftData = useAddShift();
  const editShift = useEditShift();
  const checkShiftNameMutation = useCheckShiftNameMutation();
  const [selectFirstChild, setSelectFirstChlid] = useState(false);
  const decoded = decodeJWT();
  const params = useParams();
  const url = getPageNamesFromUrl(params['*'] ?? '');

  const storeShift = async (data: any) => {
    data.id = editData?.id;

    try {
      const res = await editShift.mutateAsync(data);
      toast.success('Shift updated successfully');
      reset();
      setOpenAddShiftForm(false);
    } catch(err: any){
      console.error('err',err);
    }
  };

  const onSubmitHandler = async (data: any) => {
    try {
      if (
        String(getValues('shiftName')).trim() !== '') {
        data.shiftName = String(getValues('shiftName')).trim();

        if(editData) {
          if (String(getValues('shiftName')).trim() !== editData?.shiftName) {
            const response = await checkShiftNameMutation.mutateAsync(
              getValues('shiftName').trim()
            );

            if (response.data.id && response.data.id != editData?.id) {
              setError('shiftName', {
                type: 'manual',
                message: 'Shift Name already exist!',
              });
            } else {
              storeShift(data);
            }
          } else {
            storeShift(data);
          }
        } else {
          const response = await checkShiftNameMutation.mutateAsync(
            getValues('shiftName').trim()
          );

          if (response.data.id) {
            setError('shiftName', {
              type: 'manual',
              message: 'Shift Name already exists',
            });
          } else {
            try {
              const res = await addShiftData.mutateAsync(data);
              toast.success('Shift added successfully');
              reset();
              setOpenAddShiftForm(false);
            } catch(err: any) {
              console.error('err',err);
            }
          }
        }
      }
    } catch (err: any) {
      console.error(err.message);
    }
  };

  useEffect(() => {
    if(editData) {
      clearErrors();
      setValue('shiftName', editData?.shiftName);
      setValue('startTime', new Date(editData?.startTime)?.toLocaleTimeString('en-US', { hour:'2-digit', minute:'2-digit', hour12: false }));
      setValue('endTime', new Date(editData?.endTime)?.toLocaleTimeString('en-US', { hour:'2-digit', minute:'2-digit', hour12: false }));
      setValue('shift_type', editData?.shift_type);
    } else {
      reset();
    }
  }, [editData, reset]);

  return (
    <div>
      <div className="">
        <div className="flex justify-between mx-4">
          <div className="block">
            <h6 className="font-semibold text-[24px] text-white">
              {editData && 'Edit Shift'}
              {!editData && 'Add Shift'}
            </h6>
            <p className="font-normal my-1 text-[16px] tracking-normal leading-5 text-[#cccccc] ">
              {!editData
                ? 'Enter all of the required information to add a new shift to the platform'
                : ``}
            </p>
          </div>
          <div
            className="mt-1 cursor-pointer"
            onClick={() => {
              setOpenAddShiftForm(false);
            }}
          >
            <CloseIcon />
          </div>
        </div>
        <div className="">
          <form onSubmit={handleSubmit(onSubmitHandler)}>
            <div className="rounded pt-3">
              <div className="m-2">
                <div className="grid grid-cols-1">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Shift Name
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('shiftName')}
                      type="text"
                      name="shiftName"
                      id="shiftName"
                      className="bg-gray-200 p-1.5 w-full rounded pl-2 text-[14px] text-black"
                      placeholder="Enter Shift Name"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.shiftName?.message}
                    </p>
                  </div>
                  <div></div>
                </div>

                <div className="grid grid-cols-1">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Shift Type
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <select
                      {...register('shift_type')}
                      name="shift_type"
                      id="shift_type"
                      onChange={() => {
                        clearErrors('shift_type');
                        setSelectFirstChlid(true);
                      }}
                      className={'block w-full p-[7px] rounded bg-gray-200 focus:border-blue-500 text-[14px] text-black'}
                    >
                      <option className="hidden" value={''}>
                        Select Shift Type
                      </option>
                      <option key="Production" value="Production" className="text-black">Production</option>
                      <option key="Maintenance" value="Maintenance" className="text-black">Maintenance</option>
                    </select>
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.shift_type?.message}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 py-2">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      Start Time
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('startTime')}
                      type="time"
                      name="startTime"
                      id="startTime"
                      className="bg-gray-200 p-1.5 w-full  rounded pl-2 text-[14px] text-black"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.startTime?.message}
                    </p>
                  </div>
                  <div className=""></div>
                </div>

                <div className="grid grid-cols-1 py-2">
                  <div className="mx-2">
                    <span className="text-white text-[12px]">
                      End Time
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('endTime')}
                      type="time"
                      name="endTime"
                      id="endTime"
                      className="bg-gray-200 p-1.5 w-full  rounded pl-2 text-[14px] text-black"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.endTime?.message}
                    </p>
                  </div>
                  <div className=""></div>
                </div>
              </div>
              <div className="grid grid-cols-2 mt-5">
                <div className="mx-3">
                  <button
                    id="cancel_button"
                    title="Click to cancel"
                    onClick={() => {
                      setOpenAddShiftForm(false);
                    }}
                    className={`text-white bg-transparent border-[#4AA8FE] border-[1px] hover:border-[#4AA8FE]/75 font-medium rounded-lg text-sm px-10 py-2 text-center h-9 w-full items-center  me-2 mb-2 ${
                      editData ? 'px-6' : 'px-4'
                    }`}
                  >
                    {'Cancel'}
                  </button>
                </div>
                <div className="mx-3">
                  <button
                    id="create_shift"
                    title={
                      !editData
                        ? 'Click to add new shift details'
                        : 'Click to save shift details'
                    }
                    type="submit"
                    className={`text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 font-medium rounded-lg text-sm px-8 py-2 text-center h-9 w-full items-center me-2 mb-2 ${
                      editData ? 'px-6' : 'px-4'
                    }`}
                  >
                    {editData && 'Save'}
                    {!editData && 'Add'}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
