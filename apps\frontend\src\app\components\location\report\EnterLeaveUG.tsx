import Plot from 'react-plotly.js';
import BlankChart from '../../../../assets/BlankChart.png';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { useState, useEffect } from 'react';
import { generateWeeklyTickVals } from '../../../../utils/constant';
import TotalTimeUG from './TotalTimeUG';
import { useQuery } from '@tanstack/react-query';
import { getAllShifts } from '../../../../../src/api/shifts/shiftapis';

const EnterLeaveUG = (props: any) => {
  const redColorData = [] as any;
  const greenColorData = [] as any;
  const yellowColorData = [] as any;

  const [timeZone, setTimeZone] = useState('');

  useEffect(() => {
    const browserTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    setTimeZone(browserTimeZone);
  }, []);

  const browserTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  dayjs.extend(utc);
  dayjs.extend(timezone);
  dayjs.extend(isSameOrBefore);
  const {
    data: getShiftRes,
    status: apiStatusforShift,
    isFetching: isShiftFetching,
    isLoading: isShiftLoading,
  } = useQuery({
    queryKey: ['all-shifts'],
    queryFn: getAllShifts,
    refetchOnWindowFocus: false,
  });

  // console.log(getShiftRes?.data);

  function createStartDate(dateString: string): Date {
    const date = new Date(dateString);
    date.setHours(0, 0, 0, 0);
    return date;
  }

  function generateTimes(
    startTime: string,
    endTime: string,
    intervalMinutes: number = 120
  ): string[] {
    const times: string[] = [];
    let currentTime = new Date(`2000-01-01T${startTime}`);
    let endTimeObj = new Date(`2000-01-01T${endTime}`);

    if (currentTime >= endTimeObj) {
      endTimeObj.setDate(endTimeObj.getDate() + 1);
    }
    endTimeObj.setHours(endTimeObj.getHours() + 2);

    while (currentTime <= endTimeObj) {
      times.push(
        currentTime.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        })
      );
      currentTime.setMinutes(currentTime.getMinutes() + intervalMinutes);
    }
    const newTimes = times?.map((ele: any) => `${ele}  `);

    return newTimes;
  }

  props?.reportData?.forEach((ele: any) => {
    if (ele?.enterUGCC === 'R') {
      redColorData.push({
        x: ele?.date,
        y: timeStringToDecimal(ele?.enterUG),
        exactTime: ele?.enterUG,
      });
    }
    if (ele?.leaveUGCC === 'R') {
      redColorData.push({
        x: ele?.date,
        y: timeStringToDecimal(ele?.leaveUG),
        exactTime: ele?.leaveUG,
      });
    }
    if (ele?.enterUGCC === 'G') {
      greenColorData.push({
        x: ele?.date,
        y: timeStringToDecimal(ele?.enterUG),
        exactTime: ele?.enterUG,
      });
    }
    if (ele?.leaveUGCC === 'G') {
      greenColorData.push({
        x: ele?.date,
        y: timeStringToDecimal(ele?.leaveUG),
        exactTime: ele?.leaveUG,
      });
    }
    if (ele?.enterUGCC === 'W') {
      yellowColorData.push({
        x: ele?.date,
        y: timeStringToDecimal(ele?.enterUG),
        exactTime: ele?.enterUG,
      });
    }
    if (ele?.leaveUGCC === 'W') {
      yellowColorData.push({
        x: ele?.date,
        y: timeStringToDecimal(ele?.leaveUG),
        exactTime: ele?.leaveUG,
      });
    }
  });

  function formatTimeTo12Hour(timeString: string): string {
    const [hourStr, minuteStr] = timeString.split(':');
    let hour = parseInt(hourStr);
    const minute = minuteStr;
    const ampm = hour >= 12 ? 'PM' : 'AM';

    hour = hour % 12;
    if (hour === 0) hour = 12;

    const formattedHour = hour < 10 ? `0${hour}` : `${hour}`;

    return `${formattedHour}:${minute} ${ampm}`;
  }

  function timeStringToDecimal(
    timeStr: string | undefined
  ): number | undefined {
    if (!timeStr) {
      return undefined;
    }

    const [time, period] = timeStr.split(' ');

    if (!time || !period) {
      return undefined;
    }

    let [hours, minutes] = time.split(':');

    if (!hours || !minutes) {
      return undefined;
    }

    hours = parseInt(hours, 10);
    minutes = parseInt(minutes, 10);

    if (isNaN(hours) || isNaN(minutes)) {
      return undefined;
    }

    if (period.toUpperCase() === 'PM' && hours !== 12) {
      hours += 12;
    } else if (period.toUpperCase() === 'AM' && hours === 12) {
      hours = 0;
    }

    const decimalTime: number = hours + minutes / 60;

    return decimalTime;
  }

  function getShapes() {
    const colors = [
      '#ffbd00', // Golden Yellow
      '#fff200', // Bright Lemon
      '#ffc107', // Amber
      '#ffeb3b', // Sunshine
      '#e4d00a', // Citrine
      '#ffcc00', // Sunglow
      '#ffe135', // Banana Yellow
      '#eedc82', // Flax
      '#fcd12a', // Tuscany
      '#e6be8a', // Gold Crayola
    ];

    const shifts = props?.data?.shifts || [];

    const shapes = shifts.flatMap((shift: any, index: number) => {
      const color = colors[index % colors.length];
      return [
        {
          type: 'line',
          xref: 'paper',
          x0: 0,
          y0: timeStringToDecimal(formatTimeTo12Hour(shift.startTime)),
          x1: 1,
          y1: timeStringToDecimal(formatTimeTo12Hour(shift.startTime)),
          line: { color, width: 2, dash: 'solid' },
        },
        {
          type: 'line',
          xref: 'paper',
          x0: 0,
          y0: timeStringToDecimal(formatTimeTo12Hour(shift.endTime)),
          x1: 1,
          y1: timeStringToDecimal(formatTimeTo12Hour(shift.endTime)),
          line: { color, width: 2, dash: 'solid' },
        },
      ];
    });

    return shapes;
  }

  const shiftLineTraces = (props?.data?.shifts || []).flatMap(
    (shift: any, index: number) => {
      const colors = [
        '#ffbd00', // Golden Yellow
        '#fff200', // Bright Lemon
        '#ffc107', // Amber
        '#ffeb3b', // Sunshine
        '#e4d00a', // Citrine
        '#ffcc00', // Sunglow
        '#ffe135', // Banana Yellow
        '#eedc82', // Flax
        '#fcd12a', // Tuscany
        '#e6be8a', // Gold Crayola
      ];

      const color = colors[index % colors.length];
      const startY = timeStringToDecimal(formatTimeTo12Hour(shift.startTime));
      const endY = timeStringToDecimal(formatTimeTo12Hour(shift.endTime));

      return [
        {
          x: [props?.dateRange?.startDate, props?.dateRange?.endDate],
          y: [startY, startY],
          type: 'scatter',
          mode: 'lines',
          line: { color, width: 2, dash: 'solid' },
          name:
            getShiftRes?.data?.find((ele: any) => ele?.id == shift?.shiftId)
              ?.shiftName || '',
          showlegend: true,
          hoverinfo: 'text',
          text: ` ${
            getShiftRes?.data?.find((ele: any) => ele?.id == shift?.shiftId)
              ?.shiftName
          } Start`,
        },
        {
          x: [props?.dateRange?.startDate, props?.dateRange?.endDate],
          y: [endY, endY],
          type: 'scatter',
          mode: 'lines',
          line: { color, width: 2, dash: 'dash' },
          name:
            getShiftRes?.data?.find((ele: any) => ele?.id == shift?.shiftId)
              ?.shiftName || '',
          showlegend: false,
          hoverinfo: 'text',
          text: ` ${
            getShiftRes?.data?.find((ele: any) => ele?.id == shift?.shiftId)
              ?.shiftName
          } End`,
        },
      ];
    }
  );

  // function getAnnotations() {
  //   const colors = ['#FF5733', '#33C1FF', '#9D33FF', '#33FF8D', '#FFC733'];
  //   const shifts = props?.data?.shifts || [];

  //   const shapes = shifts.flatMap((shift: any, index: number) => {
  //     const color = colors[index % colors.length];
  //     return [
  //       {
  //         x: dayjs(props?.dateRange?.endDate)
  //           .add(1, 'day')
  //           .format('YYYY-MM-DD'),
  //         y: timeStringToDecimal(formatTimeTo12Hour(shift.startTime)),
  //         xref: 'x',
  //         yref: 'y',
  //         text: 'startTime',
  //         showarrow: false,
  //         xanchor: 'right',
  //         yanchor: 'bottom',
  //         xshift: 30,
  //         font: {
  //           color,
  //           size: 12,
  //         },
  //       },
  //       {
  //         x: dayjs(props?.dateRange?.endDate)
  //           .add(1, 'day')
  //           .format('YYYY-MM-DD'),
  //         y: timeStringToDecimal(formatTimeTo12Hour(shift.endTime)),
  //         xref: 'x',
  //         yref: 'y',
  //         text: 'endTime',
  //         showarrow: false,
  //         xanchor: 'right',
  //         yanchor: 'bottom',
  //         xshift: 30,
  //         font: {
  //           color,
  //           size: 12,
  //         },
  //       },
  //     ];
  //   });

  //   return shapes;
  // }

  const tickValues: any[] = [];
  const startDateStr = props?.dateRange?.startDate || '';
  const endDateStr = props?.dateRange?.endDate || '';

  const startDate: any = startDateStr ? dayjs.tz(startDateStr, timeZone) : null;
  const endDate: any = endDateStr ? dayjs.tz(endDateStr, timeZone) : null;
  let currentDate = startDate;
  const daysDifference = endDate.diff(startDate, 'day');

  if (startDate && endDate && startDate.isBefore(endDate)) {
    let currentDate = startDate.startOf('day');
    const endOfRange = endDate.endOf('day');

    while (
      currentDate.isBefore(endOfRange) ||
      currentDate.isSame(endOfRange, 'day')
    ) {
      tickValues.push(currentDate.format());
      currentDate = currentDate.add(1, 'day');
    }
  } else {
    console.error('Invalid date range or start date is after end date.');
  }

  let xAxisRange = generateWeeklyTickVals(
    props?.dateRange?.startDate,
    props?.dateRange?.endDate
  );
  const layout = {
    xaxis: {
      color: 'white',
      tickformat: '%b-%d',
      gridcolor: 'rgba(0, 0, 0, 0)',
      automargin: true,
      ticklen: 8,
      range:
        tickValues?.length == 1
          ? []
          : [
              props?.dateRange?.startDate,
              dayjs(props?.dateRange?.endDate)
                .add(1, 'day')
                .format('YYYY-MM-DD'),
              ,
            ],
      tickvals: xAxisRange ? xAxisRange : tickValues,
      tickangle: -45,
      autorange: true,
    },
    yaxis: {
      type: 'linear',
      color: 'white',
      range: [0, 24],
      tickvals: [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24],
      ticktext: [
        '12:00 AM',
        '02:00 AM',
        '04:00 AM',
        '06:00 AM',
        '08:00 AM',
        '10:00 AM',
        '12:00 PM',
        '02:00 PM',
        '04:00 PM',
        '06:00 PM',
        '08:00 PM',
        '10:00 PM',
        '12:00 AM',
      ],
      gridcolor: 'rgba(74, 168, 256, 50%)',
      gridwidth: 1,
      fixedrange: true,
      autoscale: true,
    },
    shapes: props?.data?.shifts?.length != 0 && getShapes(),
    // annotations: props?.data?.shifts?.length != 0 && getAnnotations(),

    legend: {
      font: {
        family: 'Arial, sans-serif',
        size: 12,
        color: 'white', // This sets a global font color for all legend items
      },
    },
    // showlegend: false,
    margin: { t: 30, l: 80, b: 45, r: 105, pad: 5 },
    automargin: true,
    plot_bgcolor: 'rgba(0, 0, 0, 0)',
    paper_bgcolor: 'rgba(0, 0, 0, 0)',
    dragmode: 'pan',
    modeBarButtonsToRemove: ['lasso2d', 'select2d'],
    displaylogo: false,
  };

  const data = [
    {
      x:
        tickValues?.length == 1
          ? yellowColorData.map((point: any) =>
              dayjs(new Date(point?.x)).format('MMM-DD')
            )
          : yellowColorData.map((point: any) => point.x),
      y: yellowColorData.map((point: any) => point.y),
      mode: 'markers',
      marker: { color: 'rgba(255,177,50,100%)', size: 10 },
      type: 'scatter',
      name: '',
      hoverinfo: 'text',
      text: yellowColorData?.map(
        (point: any) => `${dayjs(point.x).format('DD-MMM')}, ${point.exactTime}`
      ),
      showlegend: false,
    },
    {
      x:
        tickValues?.length == 1
          ? redColorData.map((point: any) =>
              dayjs(new Date(point?.x)).format('MMM-DD')
            )
          : redColorData.map((point: any) => point.x),
      y: redColorData?.map((point: any) => point?.y),
      mode: 'markers',
      marker: { color: 'rgba(254,74,106,100%)', size: 10 },
      type: 'scatter',
      name: '',
      hoverinfo: 'text',
      text: redColorData?.map(
        (point: any) => `${dayjs(point.x).format('DD-MMM')}, ${point.exactTime}`
      ),
      showlegend: false,
    },
    {
      x:
        tickValues?.length == 1
          ? greenColorData.map((point: any) =>
              dayjs(new Date(point?.x)).format('MMM-DD')
            )
          : greenColorData.map((point: any) => point.x),
      y: greenColorData.map((point: any) => point.y),
      mode: 'markers',
      marker: { color: 'rgba(150,251,96,100%)', size: 10 },
      type: 'scatter',
      name: '',
      hoverinfo: 'text',
      text: greenColorData?.map(
        (point: any) => `${dayjs(point.x).format('DD-MMM')}, ${point.exactTime}`
      ),
      showlegend: false,
    },
    {
      x:
        props?.data?.avgEnterUG && tickValues?.length != 1
          ? [tickValues[0], tickValues[tickValues?.length - 1]]
          : [],
      y:
        props?.data?.avgEnterUG && tickValues?.length != 1
          ? [
              timeStringToDecimal(props?.data?.avgEnterUG),
              timeStringToDecimal(props?.data?.avgEnterUG),
            ]
          : [],
      mode: 'lines',
      name: '',
      line: {
        dash: 'dot',
        width: 0,
        color: 'rgba(0, 0, 0, 0)',
      },
      hoverinfo: 'none',
      showlegend: false,
    },
    ...shiftLineTraces,
  ];
  console.log('props', data);

  // return (
  //   <div className="w-[90%] m-auto">
  //     {props?.data?.avgEnterUG || props?.reportData.length != 0 ? (
  //       <Plot
  //         data={data}
  //         layout={layout}
  //         style={{ width: '100%' }}
  //         className=" !h-[56vh] relative top-0 left-0"
  //         config={{
  //           displayModeBar: false,
  //           scrollZoom: true,
  //           responsive: true,
  //           dragmode: false,
  //         }}
  //       />
  //     ) : (
  //       <div className="mt-10">
  //         <img src={BlankChart} alt="" className="w-full h-[40vh]" />
  //       </div>
  //     )}
  //   </div>
  // );
  return (
    <div className="flex w-full">
      <div className="w-1/2">
        {props?.data?.avgEnterUG || props?.reportData.length !== 0 ? (
          <>
            <Plot
              data={data}
              layout={layout}
              className="!w-full !h-[60vh] relative top-0 left-0"
              config={{
                displayModeBar: false,
                scrollZoom: true,
                responsive: true,
                dragmode: false,
              }}
            />
            <div className="flex justify-center text-[20px] text-white">
              Enter/Leave UG
            </div>
          </>
        ) : (
          <div className="mt-10">
            <img src={BlankChart} alt="" className="w-full h-[40vh]" />
          </div>
        )}
      </div>

      <div className="w-1/2">
        {/* Example: Uncomment and pass props if needed */}
        <TotalTimeUG {...props} />
      </div>
    </div>
  );
};

export default EnterLeaveUG;
