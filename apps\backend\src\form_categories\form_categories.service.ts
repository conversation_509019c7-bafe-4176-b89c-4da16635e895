import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateFormCategoryDto } from './dto/create-form-category.dto';
import { FormCategory } from './entities/form_category.entity';
@Injectable()
export class FormCategoriesService {
  constructor(
    @InjectRepository(FormCategory) private formCategoryRepo: Repository<FormCategory>
  ) {}

  async create(formCategoryDto: CreateFormCategoryDto): Promise<FormCategory> {
    const formCategory = this.formCategoryRepo.create(formCategoryDto);

    const res = await this.formCategoryRepo.save(formCategory);
    return res;
  }

  async find(): Promise<FormCategory[]> {
    const formCategories = await this.formCategoryRepo.find();
    return formCategories;
  }

  async findOne(id: number): Promise<FormCategory> {
    const FormCategory = this.formCategoryRepo.findOne({
      where: {
        id,
      },
    });

    if (!FormCategory) {
      throw new NotFoundException('Form Category Not Found!');
    }
    return FormCategory;
  }
}
