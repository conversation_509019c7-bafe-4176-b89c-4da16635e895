import { Injectable, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { FormTemplateDefinition } from './entities/form_template_definitions.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateFormTemplateDefinitionDto } from './dto/create-form-template-definitions.dto';
import { UpdateFormTemplateDefinitionDto } from './dto/update-form-template-definitions.dto';

@Injectable()
export class FormTemplateDefinitionsService {
  constructor(
    @InjectRepository(FormTemplateDefinition)
    private definitionRepo: Repository<FormTemplateDefinition>
  ) {}

  async create(createFormTemplateDefinitionDto: CreateFormTemplateDefinitionDto) {
    return await this.definitionRepo.save(createFormTemplateDefinitionDto);
  }

  async findAll(mineId: number) {
    return await this.definitionRepo.find({
      where: {
        mineId: mineId,
        isDelete: false
      },
    });
  }

  async findOne(id: number) {
    return await this.definitionRepo.findOne({
      where: { id },
    });
  }

  async findLatestByFormTemplateId(formTemplateId: number) {
    return await this.definitionRepo.findOne({
      where: {
        formTemplateId: formTemplateId,
        isDelete: false
      },
      order: { major: 'DESC', minor: 'DESC', revision: 'DESC'}
    });
  }

  async revise(id: number, createFormTemplateDefinitionDto: CreateFormTemplateDefinitionDto, user:any) {
    const formTemplateDefinitionToRevise = await this.definitionRepo.findOne({
      where: {
        formTemplateId: id,
        isDelete: false
      },
      order: { major: 'DESC', minor: 'DESC', revision: 'DESC'}
    });
    let newFormTemplateDefinition: FormTemplateDefinition = {
      ...formTemplateDefinitionToRevise,
      revision: formTemplateDefinitionToRevise.revision+1,
      definition: createFormTemplateDefinitionDto.definition,
      createdBy: user.userId,
      mineId: user.mineid,
      isPublished: false,
    };
    delete newFormTemplateDefinition.id;
    delete newFormTemplateDefinition.publishedAt;
    delete newFormTemplateDefinition.publishedBy;
    delete newFormTemplateDefinition.updatedAt;
    delete newFormTemplateDefinition.updatedBy;
    if(formTemplateDefinitionToRevise.isPublished) {
      newFormTemplateDefinition.minor = formTemplateDefinitionToRevise.minor+1;
      newFormTemplateDefinition.revision = 0;
    }

    return await this.definitionRepo.save(newFormTemplateDefinition);
  }

  async update(id: number, updateFormTemplateDefinitionDto: UpdateFormTemplateDefinitionDto, user:any) {
    const formTemplateDefinitionToUpdate = await this.definitionRepo.findOneBy({ id });

    if (!formTemplateDefinitionToUpdate) {
      throw new NotFoundException('FormTemplateDefinition not Found!');
    }

    if(formTemplateDefinitionToUpdate.isPublished) {
      let newDefinition: FormTemplateDefinition = {
        ...formTemplateDefinitionToUpdate,
        formTemplateId: id,
        mineId: user.mineid,
        definition: updateFormTemplateDefinitionDto.definition,
        isPublished: false,
        major: formTemplateDefinitionToUpdate.major,
        minor: (formTemplateDefinitionToUpdate.minor + 1),
        revision: 0,
        createdBy: user.userId,
        updatedBy: user.userId,
      }

      return await this.definitionRepo.save(newDefinition);
    }
    
    const updatedFormTemplateDefinition = Object.assign(formTemplateDefinitionToUpdate, updateFormTemplateDefinitionDto);
    return await this.definitionRepo.save(updatedFormTemplateDefinition);
  }

  async publish(id: number, user:any) {
    const formTemplateDefinitionToPublish =
      await this.definitionRepo.findOne({
        where: { id },
        select: {
          id: true,
          formTemplateId: true,
        }
      });

    if (!formTemplateDefinitionToPublish) {
      throw new NotFoundException('FormTemplateDefinition not Found!');
    }
    const formTemplateId = formTemplateDefinitionToPublish.formTemplateId;

    await this.definitionRepo
      .createQueryBuilder('form_template_definitions')
      .update(FormTemplateDefinition)
      .set({isPublished: false})
      .where("formTemplateId = :formTemplateId", {formTemplateId: formTemplateId})
      .execute();

    await this.definitionRepo
      .createQueryBuilder('form_template_definitions')
      .update(FormTemplateDefinition)
      .set({isPublished: true, publishedBy: user.userId, publishedAt: new Date()})
      .where("id = :id", {id : id})
      .execute();
      
    return await this.definitionRepo.findOne({
        where: { id },
        select: {
          id: true,
          formTemplateId: true,
          isPublished: true,
          publishedBy: true,
        }
      });
  }

  async deleteDefinition(id: number) {
    let definition = await this.findOne(id);
    if (!definition) {
      throw new NotFoundException(`definition with given id ${id} is not found`);
    } else {
      if (definition.isDelete === true) {
        definition.isDelete = false;
      } else {
        definition.isDelete = true;
      }
      return this.definitionRepo.save(definition);
    }
  }
}
