import { useEffect, useRef, useState } from 'react';
import {
  CloneIcon,
  EditIcon,
  EyeIcon,
  SearchIcon,
  TrashIcon,
} from '../../../assets/icons/icons';
import Table, { ColumnDef } from '../common/Table';
import AddTemplateForm from './templates/AddTemplateForm';
import Modal from '../common/Modal';
import { useQuery } from '@tanstack/react-query';
import { getFormTemplates } from '../../../api/forms/formtemplateapis';
import {
  useDeleteFormTemplate,
} from '../../../services/mutations/formtemplatemutations';
import { toast } from 'react-toastify';
import decodeJWT from '../../../utils/jwtdecoder';
import { Tooltip } from '@material-tailwind/react';
import { escapeRegExp } from '../../../utils/constant';
import { getFeatures } from '../../../api/users/userapis';
import Loader from '../common/Loader';
import mixpanel from 'mixpanel-browser';
import { getPageNamesFromUrl } from '../PageName';
import { useNavigate, useParams } from 'react-router-dom';

export default function FormTemplates() {
  const [openAddTemplateForm, setOpenAddFormTemplateForm] = useState(false);
  const [editData, setEditData] = useState(null);
  const [cloneForm, setCloneForm] = useState(false);
  const [templateData, setTemplateData] = useState<any>();
  const [searchPh, setSearchPh] = useState('');
  const [openDeleteModal, setOpenDeteteModal] = useState(false);
  const [tableCount, setTableCount] = useState();

  const {status, data, isLoading } = useQuery({
    queryKey: ['form-templates'],
    queryFn: getFormTemplates,
    refetchOnWindowFocus: false,
  });
  const { data: features } = useQuery({
    queryKey: ['features'],
    queryFn: () => getFeatures(),
    refetchOnWindowFocus: false,
  });
  
  const navigate = useNavigate();
  const deleteFormTemplate = useDeleteFormTemplate();
  const decoded = decodeJWT();
  const params = useParams();
  const url = getPageNamesFromUrl(params['*'] ?? '');
  
  useEffect(() => {
    const handleOverflow = (cell: any) => {
      if (cell.offsetWidth < cell.scrollWidth) {
        cell.setAttribute('title', cell.innerText);
      } else {
        cell.removeAttribute('title');
      }
    };

    const observeDataChanges = () => {
      const tableCells = document?.querySelectorAll('.text-ellipsis');
      if (tableCells) {
        tableCells.forEach((cell) => {
          handleOverflow(cell);
        });
      }
    };

    observeDataChanges(); // Initial observation

    const observer = new MutationObserver(observeDataChanges);
    observer.observe(document.body, { subtree: true, childList: true });

    return () => {
      observer.disconnect();
    };
  }, [data?.data, status]);

  const regex = new RegExp(`(${escapeRegExp(searchPh)})`, 'i');
  const columns: ColumnDef[] = [
    {
      key: 'name',
      label: 'Name',
      type: 'text',
      truncate: false,
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 cursor-pointer"
            title="View Form"
            data-tooltip-target="tooltip-default"
            onClick={async () => {
              navigate(
                params?.mineId
                ? `/app/Mines/${params?.mineId}/Forms/Templates/${row.formTemplateId}`
                : `/app/Forms/Templates/${row.formTemplateId}`
              );
            }}>
          <u>{row.name}</u>
        </div>
      ),
    },
    {
      key: 'category',
      label: 'Category',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.category}
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row.isPublished ? 'Published' : 'Draft'}
        </div>
      ),
    },
    {
      key: 'version',
      label: 'Version',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {`${row.major}.${row.minor}.${row.revision}`}
        </div>
      ),
    },
    {
      key: 'savedBy',
      label: 'Saved By',
      type: 'text',
      render: (row: any) => (
        <div className="text-ellipsis overflow-hidden w-35 ">
          {row?.updatedByUser ?? row?.createdByUser ?? '-'}
        </div>
      ),
    },
    {
      key: 'savedAt',
      label: 'Saved On',
      type: 'text',
      render: (row: any) => {
        let dateStr = row?.updatedAt ?? row?.submittedAt ?? null;
        let date = '-';
        if(dateStr) {
          date = `${dateStr.split('T')[0]} ${dateStr.split('T')[1].split('.')[0]}`
        }
        return (
          <div className="text-ellipsis overflow-hidden w-35 ">
            {date}
          </div>
        )
      },
    },
    {
      key: 'action',
      label: 'Actions',
      type: 'element',
      render: (row) => (
        <div className="flex justify-center">
          <div
            title="Clone Template"
            className="cursor-pointer"
            data-tooltip-target="tooltip-default"
            onClick={async () => {
              let clonedForm = {
                ...row,
                name: `${row.name} clone`,
                isPublished: false,
                major: 1,
                minor: 0,
                revision: 0,
              };
              setCloneForm(true);
              setEditData(clonedForm);
              setOpenAddFormTemplateForm(true);
            }}
          >
            <CloneIcon className="text-white font-black h-5 w-5 mx-2 edit-icon" />
          </div>
          <div
            title="Edit Template"
            className="cursor-pointer"
            data-tooltip-target="tooltip-default"
            onClick={() => {
              setEditData(row);
              setOpenAddFormTemplateForm(true);
              document
                ?.getElementsByClassName('scrollToTop')[0]
                ?.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center',
                });
            }}
          >
            <EditIcon className="text-white font-black h-5 w-5 mx-2  edit-icon" />
          </div>
          {!row.isPublished &&
            <div
              title="Delete Template"
              className="cursor-pointer"
              data-tooltip-target="tooltip-default"
              onClick={() => {
                setTemplateData(row);
                setOpenDeteteModal(true);
                document
                  ?.getElementsByClassName('scrollToTop')[0]
                  ?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                  });
              }}
            >
              <TrashIcon className="text-white font-black text-[14px] h-5 w-5 mx-2 delete_icon" />
            </div>
          }
        </div>
      ),
    },
  ];

  useEffect(() => {
    if (localStorage.getItem('refreshingPage')) {
      localStorage.removeItem('refreshingPage');
      setTimeout(() => {
        toast.info('Template updated');
      }, 1000);
    }
  });

  if (
    features?.data.some((feature: any) => feature.FeatureName == 'Users') ||
    decodeJWT()?.role == 'superuser'
  ) {
    return (
      <div className="w-full">
        <div className="sticky w-full top-0 z-30  box  bg-top px-10 2xl:px-16">
          <div className="grid grid-cols-3 border-b-[1px] border-[#80c2fe]  pb-4 pt-4 ">
            <div className="">
              <h6 className="p-2 font-bold text-white text-[32px] text-left">
                Templates
              </h6>
            </div>
            <div className=" h-12 w-72">
              <div className="relative pt-3">
                <span>
                  <SearchIcon className="absolute top-0 left-2 h-6 mr-1 my-5 cursor-pointer"></SearchIcon>
                </span>
                <input
                  id="search-result"
                  type="text"
                  className={`border text-white text-sm pl-8 outline-none bg-transparent rounded border-[#4AA8FE] block w-full p-2 autoComOff`}
                  placeholder="Search Template"
                  autoFocus
                  value={searchPh}
                  onChange={(e: any) => {
                    setSearchPh(e?.target?.value);
                  }}
                />
              </div>
            </div>
            <div className="">
              <h6 className="p-2 font-bold text-white text-[32px] text-right">
                Forms
              </h6>
            </div>
          </div>
        </div>
        <div className="mt-8 px-10 2xl:px-16">
          <div>
            {openAddTemplateForm ? (
              <Modal
                Content={
                  <AddTemplateForm
                    cloneForm={cloneForm}
                    setCloneForm={setCloneForm}
                    editData={editData}
                    setOpenAddFormTemplateForm={setOpenAddFormTemplateForm}
                  />
                }
                size="w-2/4 2xl:w-2/5 xl:w-2/5"
                backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
              ></Modal>
            ) : (
              ''
            )}
          </div>
          <div>
            {isLoading ? (
              <div>
                <div>
                  <div className="flex justify-center items-center h-full pt-[200px] white">
                    {<Loader />}
                  </div>
                  <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                    Loading....
                  </div>
                </div>
              </div>
            ) : (
              <div className="pt-8 pb-2 userMTableBg px-5 rounded-xl">
                <div className="flex justify-between">
                  <div className="block">
                    <h6 className="font-semibold text-[20px] text-white">
                      {`Form Templates (${tableCount ? tableCount : 0})`}
                    </h6>
                    <p className="font-normal my-1 text-[14px] tracking-normal leading-5 text-[#cccccc] ">
                      Manage existing form templates
                    </p>
                  </div>
                  <div>
                    <button
                      id="add_new_template"
                      title="Click to open add template form"
                      className="text-white bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 font-medium rounded-lg text-sm px-10 py-2 text-center  items-center  me-2 mb-2"
                      onClick={() => {
                        setEditData(null);
                        setOpenAddFormTemplateForm(true);
                      }}
                    >
                      Add New Template
                    </button>
                  </div>
                </div>
                <div className="mt-5">
                  {data?.data.length > 0 ? (
                    <div>
                      <Table
                        columns={columns}
                        data={
                          data?.data?.map((ele: any) => ({
                            name:
                              ele?.name,
                            ...ele,
                          })) ?? []
                        }
                        searchText={searchPh}
                        sortable={false}
                        searchOnColumn={'name'}
                        separateLine={false}
                        scrollable={true}
                        dataRenderLimitMdScreen={8}
                        dataRenderLimitLgScreen={15}
                        tableHeightClassLg="tableheightForlg"
                        tableHeightClassMd="tableheightFormd"
                        setTableCount={setTableCount}
                      />
                    </div>
                  ) : (
                    <div className="text-[26px] font-bold  text-center text-white">
                      There are currently no form templates
                    </div>
                  )}
                </div>

                {openDeleteModal ? (
                  <Modal
                    size="w-2/4 2xl:w-1/3 xl:w-2/4"
                    Content={
                      <div className="p-4">
                        <div className="text-[24px] text-white text-center ">
                          Are you sure you want to delete?
                        </div>
                        <div className="my-2 text-[56px] text-[#4AA8FE]  text-center text-provima  text-ellipsis overflow-hidden m-auto">
                          {templateData?.name}
                        </div>
                        <div className="mt-2 text-center">
                          <button
                            title="Click to Cancel"
                            id="cancel_button"
                            onClick={() => setOpenDeteteModal(!openDeleteModal)}
                            className="my-2  text-white hover:border-[#4AA8FE]/75 text-[14px] py-2 px-8 rounded focus:outline-none focus:shadow-outline border border-[#4AA8FE] mr-2"
                          >
                            Cancel
                          </button>
                          <button
                            title="Click to delete template"
                            id="delete_button"
                            onClick={async (e: any) => {
                              e.preventDefault();
                              try {
                                const res = await deleteFormTemplate.mutateAsync(
                                  templateData?.formTemplateId
                                );
                                if (res?.status == 200 || res?.status == 201) {
                                  setOpenDeteteModal(!openDeleteModal);
                                  setTemplateData({});
                                  toast.success('Template deleted successfully');
                                }
                              } catch (err: any) {
                                toast.error(err.message);
                                mixpanel.track('Error Event', {
                                  Page_Name: url,
                                  Action_Name: 'Delete Template'
                                })
                              }
                            }}
                            className="my-2 bg-[#4AA8FE] hover:bg-[#4AA8FE]/75 hover:border-[#4AA8FE]/75 text-white text-[14px] py-2 px-8 rounded focus:outline-none focus:shadow-outline border border-[#4AA8FE]"
                          >
                            Yes, Permanently Delete
                          </button>
                        </div>
                      </div>
                    }
                    backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
                  />
                ) : (
                  <></>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  } else {
    return '';
  }
}
