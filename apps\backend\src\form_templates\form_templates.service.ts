import { Injectable, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { FormTemplate } from './entities/form_template.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateFormTemplateDto } from './dto/create-form-template.dto';
import { UpdateFormTemplateDto } from './dto/update-form-template.dto';
import { CreateFormTemplateDefinitionDto } from '../form_template_definitions/dto/create-form-template-definitions.dto';
import { FormTemplateDefinition } from '../form_template_definitions/entities/form_template_definitions.entity';
import { FormTemplateDefinitionsService } from '../form_template_definitions/form_template_definitions.service';

@Injectable()
export class FormTemplatesService {
  constructor(
    private definitionService: FormTemplateDefinitionsService,
    @InjectRepository(FormTemplate)
    private formTemplateRepository: Repository<FormTemplate>,
    @InjectRepository(FormTemplateDefinition)
    private definitionRepo: Repository<FormTemplateDefinition>
  ) {}

  async create(createFormTemplateDto: CreateFormTemplateDto, user?: any) {
    let newFormTemplate: FormTemplate;
    createFormTemplateDto = {
      name: createFormTemplateDto.name,
      description: createFormTemplateDto.description,
      formCategoryId: createFormTemplateDto.formCategoryId,
      mineId: user.mineid,
      createdBy: user.userId
    };
    newFormTemplate = this.formTemplateRepository.create(createFormTemplateDto);
    const savedFormTemplate = await this.formTemplateRepository.save(newFormTemplate);

    let definition: CreateFormTemplateDefinitionDto;
    definition = {
      definition: '{}',
      mineId: user.mineid,
      formTemplateId: savedFormTemplate.id,
      major: 1,
      minor: 0,
      revision: 0,
      createdBy: user.userId,
    };
    
    await this.definitionRepo.save(definition);
    return savedFormTemplate;
  }

  async findAll(mineId: number) {
    const formTemplateData = await this.formTemplateRepository
      .createQueryBuilder('ft')
      .select([
        'ft.id AS formTemplateId',
        'ft.name AS name',
        'ft.description AS description',
        'ft.formCategoryId AS formCategoryId',
        'ft.mineId AS mineId',
        'fc.name AS category',
        'ftd.major AS major',
        'ftd.minor AS minor',
        'ftd.revision AS revision',
        'ftd.isPublished AS isPublished',
        'u1.username AS createdByUser',
        'u2.username AS updatedByUser',
        'u3.username AS publishedByUser',
        'ftd.createdAt AS createdAt',
        'ftd.updatedAt AS updatedAt',
        'ftd.publishedAt AS publishedAt',
      ])
      .innerJoin('form_categories', 'fc', 'fc.id = ft.formCategoryId')
      .leftJoin('form_template_definitions', 'ftd', 'ftd.id = (SELECT MAX(subftd.id) FROM form_template_definitions subftd WHERE subftd.form_template_id = ft.id)')
      .leftJoin('users', 'u1', 'u1.id = ftd.createdBy')
      .leftJoin('users', 'u2', 'u2.id = ftd.updatedBy')
      .leftJoin('users', 'u3', 'u3.id = ftd.publishedBy')
      .where('ft.mineId = :mineId', { mineId: mineId })
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .andWhere('ftd.is_delete = :isDelete', { isDelete: false })
      .addOrderBy('ftd.major', 'DESC')
      .addOrderBy('ftd.minor', 'DESC')
      .addOrderBy('ftd.revision', 'DESC')
      .getRawMany();
    return formTemplateData;
  }

  async findTemplateHistory(id: number) {
    const formTemplateHistory = await this.formTemplateRepository
      .createQueryBuilder('ft')
      .select([
        'ftd.id AS id',
        'ft.id AS formTemplateId',
        'ft.name AS name',
        'ft.description AS description',
        'ft.formCategoryId AS formCategoryId',
        'ft.mineId AS mineId',
        'fc.name AS category',
        'ftd.major AS major',
        'ftd.minor AS minor',
        'ftd.revision AS revision',
        'ftd.isPublished AS isPublished',
        'u1.username AS createdByUser',
        'u2.username AS updatedByUser',
        'u3.username AS publishedByUser',
        'ftd.publishedAt AS publishedAt',
        'ftd.createdAt AS createdAt',
        'ftd.updatedAt AS lastSaved'
      ])
      .innerJoin('form_categories', 'fc', 'fc.id = ft.formCategoryId')
      .innerJoin('form_template_definitions', 'ftd', 'ft.id = ftd.formTemplateId')
      .leftJoin('users', 'u1', 'u1.id = ftd.createdBy')
      .leftJoin('users', 'u2', 'u2.id = ftd.updatedBy')
      .leftJoin('users', 'u3', 'u3.id = ftd.publishedBy')
      .where('ft.id = :id', { id: id })
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .andWhere('ftd.is_delete = :isDelete', { isDelete: false })
      .addOrderBy('ftd.major', 'DESC')
      .addOrderBy('ftd.minor', 'DESC')
      .addOrderBy('ftd.revision', 'DESC')
      .limit(10)
      .getRawMany();
    return formTemplateHistory;
  }

  async findAllPublished(mineId: number) {
    let formTemplateData = await this.formTemplateRepository
      .createQueryBuilder('ft')
      .select([
        'ft.id AS formTemplateId',
        'ft.name AS name', 
        'ft.description AS description',
        'ft.formCategoryId AS formCategoryId',
        'ft.mineId AS mineId',
        'fc.name AS category',
        'ftd.major AS major',
        'ftd.minor AS minor',
        'ftd.revision AS revision',
        'u1.username AS createdByUser',
        'u2.username AS updatedByUser',
        'u3.username AS publishedByUser',
        'ftd.publishedAt AS publishedAt',
        'ftd.createdAt AS createdAt',
        'ftd.updatedAt AS lastSaved'
      ])
      .innerJoin('form_categories', 'fc', 'fc.id = ft.formCategoryId')
      .innerJoin('form_template_definitions', 'ftd', 'ftd.formTemplateId = ft.id')
      .leftJoin('users', 'u1', 'u1.id = ftd.createdBy')
      .leftJoin('users', 'u2', 'u2.id = ftd.updatedBy')
      .leftJoin('users', 'u3', 'u3.id = ftd.publishedBy')
      .where('ft.mineId = :mineId', { mineId: mineId })
      .andWhere('ftd.isPublished = :isPublished', {isPublished: true})
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .andWhere('ftd.is_delete = :isDelete', { isDelete: false })
      .addOrderBy('ftd.major', 'DESC')
      .addOrderBy('ftd.minor', 'DESC')
      .addOrderBy('ftd.revision', 'DESC')
      .getRawMany();
    return formTemplateData;
  }

  async findPublishedRevision(templateId: number) {
    let formTemplateData = await this.formTemplateRepository
      .createQueryBuilder('ft')
      .select([
        'ft.id AS formTemplateId',
        'ft.formCategoryId AS formCategoryId',
        'ftd.id AS formTemplateDefinitionId',
        'ft.name AS name',
        'ft.description AS description',
        'fc.name AS category',
        'ftd.definition AS definition',
        'ftd.major AS major',
        'ftd.minor AS minor',
        'ftd.revision AS revision',
        'ftd.isPublished AS isPublished'
      ])
      .innerJoin('form_categories', 'fc', 'fc.id = ft.formCategoryId')
      .innerJoin('form_template_definitions', 'ftd', 'ftd.formTemplateId = ft.id')
      .where('ft.id = :templateId', {templateId: templateId})
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .andWhere('ftd.is_delete = :isDelete', { isDelete: false })
      .andWhere('ftd.isPublished = :isPublished', {isPublished: true})
      .addOrderBy('ftd.major', 'DESC')
      .addOrderBy('ftd.minor', 'DESC')
      .addOrderBy('ftd.revision', 'DESC')
      .getRawOne();
    return formTemplateData;
  }

  async findLatestRevision(templateId: number) {
    let formTemplateData = await this.formTemplateRepository
      .createQueryBuilder('ft')
      .select([
        'ft.id AS formTemplateId',
        'ft.formCategoryId AS formCategoryId',
        'ftd.id AS formTemplateDefinitionId',
        'ft.name AS name',
        'ft.description AS description',
        'fc.name AS category',
        'ftd.definition AS definition',
        'ftd.major AS major',
        'ftd.minor AS minor',
        'ftd.revision AS revision',
        'ftd.isPublished AS isPublished'
      ])
      .innerJoin('form_categories', 'fc', 'fc.id = ft.formCategoryId')
      .innerJoin('form_template_definitions', 'ftd', 'ftd.formTemplateId = ft.id')
      .where('ft.id = :templateId', {templateId: templateId})
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .andWhere('ftd.is_delete = :isDelete', { isDelete: false })
      .addOrderBy('ftd.major', 'DESC')
      .addOrderBy('ftd.minor', 'DESC')
      .addOrderBy('ftd.revision', 'DESC')
      .getRawOne();
    return formTemplateData;
  }

  async findRevision(definitionId: number) {
    let formTemplateData = await this.formTemplateRepository
      .createQueryBuilder('ft')
      .select([
        'ft.id AS formTemplateId',
        'ft.formCategoryId AS formCategoryId',
        'ftd.id AS formTemplateDefinitionId',
        'ft.name AS name',
        'ft.description AS description',
        'fc.name AS category',
        'ftd.definition AS definition',
        'ftd.major AS major',
        'ftd.minor AS minor',
        'ftd.revision AS revision',
        'ftd.isPublished AS isPublished'
      ])
      .innerJoin('form_categories', 'fc', 'fc.id = ft.formCategoryId')
      .innerJoin('form_template_definitions', 'ftd', 'ftd.formTemplateId = ft.id')
      .where('ftd.id = :definitionId', {definitionId: definitionId})
      .andWhere('ft.is_delete = :isDelete', { isDelete: false })
      .andWhere('ftd.is_delete = :isDelete', { isDelete: false })
      .getRawOne();
    return formTemplateData;
  }

  async findOne(id: number) {
    return await this.formTemplateRepository.findOne({
      where: { id },
    });
  }

  async findByName(name: string) {
    const formTemplateData = await this.formTemplateRepository.findOneBy({
      name: name,
    });

    return formTemplateData;
  }

  async update(id: number, updateFormTemplateDto: UpdateFormTemplateDto, user?: any) {
    const formTemplateToUpdate = await this.formTemplateRepository.findOneBy({ id });

    if (!formTemplateToUpdate) {
      throw new NotFoundException('FormTemplate not Found!');
    }
    formTemplateToUpdate.updatedBy = user.userId;
    const updatedFormTemplate = Object.assign(formTemplateToUpdate, updateFormTemplateDto);
    return await this.formTemplateRepository.save(updatedFormTemplate);
  }

  async clone(id: number, createFormTemplateDto: CreateFormTemplateDto, user?: any) {
    let newFormTemplate: FormTemplate;
    createFormTemplateDto = {
      name: createFormTemplateDto.name,
      description: createFormTemplateDto.description,
      formCategoryId: createFormTemplateDto.formCategoryId,
      mineId: user.mineid,
      createdBy: user.userId
    };
    newFormTemplate = this.formTemplateRepository.create(createFormTemplateDto);
    const savedFormTemplate = await this.formTemplateRepository.save(newFormTemplate);

    const findDefinition = await this.definitionService.findLatestByFormTemplateId(id);

    let definition: CreateFormTemplateDefinitionDto;
    definition = {
      definition: '{}',
      mineId: user.mineid,
      formTemplateId: savedFormTemplate.id,
      major: 1,
      minor: 0,
      revision: 0,
      createdBy: user.userId,
    };

    if(findDefinition) {
      definition.definition = findDefinition.definition;
    }
    
    await this.definitionRepo.save(definition);
    return savedFormTemplate;
  }

  async deleteTemplate(id: number, user?: any) {
    let template = await this.findOne(id);
    if (!template) {
      throw new NotFoundException(`template with given id ${id} is not found`);
    } else {
      if (template.isDelete === true) {
        template.isDelete = false;
      } else {
        template.isDelete = true;
      }
      template.updatedBy = user.userId;
      return this.formTemplateRepository.save(template);
    }
  }

  async uploadFile(file: Express.Multer.File) {
    console.log(file);
  }
}
