import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import {
	useCheckTemplateNameMutation,
	useEditFormTemplate,
} from '../../../../../services/mutations/formtemplatemutations';
import { usePublishFormTemplateDefinition } from '../../../../../services/mutations/formtemplatemutations';
import { useQuery } from '@tanstack/react-query';
import { getFormCategories } from '../../../../../api/forms/formcategoryapis';
import { useNavigate, useParams } from 'react-router-dom';
import { Id, Template } from '../../types';
import { toast } from 'react-toastify';
import { getFormTemplateHistory } from '../../../../../api/forms/formtemplateapis';
import FormToolButton from '../tools/FormToolButton';
import { ExportIcon, ImportIcon, PlusIcon } from '../../../../../assets/icons/icons';

const schema = yup.object({
  name: yup
  	.string()
	.max(50, 'Template name should not exceed 50 characters')
	.required('Please enter a template name'),
  categoryId: yup.string().required('Please select a category'),
  description: yup
  	.string()
	.max(100, 'Template description should not exceed 100 characters')
	.required('Please enter a template description'),
});

interface Props {
	currentRevisionId: Id;
	template: Template;
	publishActive: boolean;
	setPublishActive: (value: boolean) => void;
	exportFormJSON: () => void;
	importFormJSON: () => void;
	selectHistoricalRecord: (id: Id) => void;
	createRevision: () => void;
}

export default function FormDetails({
	currentRevisionId,
	template,
	publishActive,
	setPublishActive,
	exportFormJSON,
	importFormJSON,
	selectHistoricalRecord,
	createRevision
}: Props) {
	const {details, properties } = template;
	const {
		register,
		handleSubmit,
		formState: { errors },
		setValue,
		reset,
		watch,
		getValues,
		setError,
		clearErrors,
	} = useForm({
		resolver: yupResolver(schema),
	});
	
	const editTemplateDetails = useEditFormTemplate();
	const checkTemplateNameMutation = useCheckTemplateNameMutation();
	const publishFormTemplateDefinition = usePublishFormTemplateDefinition();
	const [selectFirstChild, setSelectFirstChild] = useState(false);
	const [activeTab, setActiveTab] = useState('details');
	const [editData, setEditData] = useState({id: details.id, name: details.name, categoryId: details.categoryId, description: details?.description});

	const navigate = useNavigate();
	const params = useParams();

	const { status: formCategoryStatus, data: formCategories, isLoading: categoriesLoading } = useQuery({
		queryKey: ['formCategories'],
		queryFn: () => getFormCategories(),
		refetchOnWindowFocus: false,
	});

	const { status: historyStatus, data: formTemplateHistory, isLoading: historyLoading } = useQuery({
		queryKey: ['form-template-history'],
		queryFn: () => getFormTemplateHistory(params?.formTemplateId),
		refetchOnWindowFocus: false,
	});

	async function handleFormHistoryRowClick(e: React.MouseEvent<HTMLTableRowElement>, id: Id) {
		e.preventDefault();
		if(currentRevisionId != id) {
			selectHistoricalRecord(id);
		}
	}

	const handleInputChange = async () => {
		try {
			if(editData.name !== details.name ||
				editData.categoryId !== details.categoryId ||
				editData.description !== details.description) {
				
				if(editData.name.trim() === '') {
					setError('name', {
						type: 'manual',
						message: 'Template must have a name!',
					});
				}
				else if(editData.description.trim() == '') {
					setError('description', {
						type: 'manual',
						message: 'Template must have a description!',
					});
				}
				else {
					const res = await checkTemplateNameMutation.mutateAsync(
						editData.name.trim()
					);
					if(res.data.id && res.data.id !== editData.id) {
						setError('name', {
							type: 'manual',
							message: 'Template name already exists!',
						});
					} else {
						try {
							const res:any = await editTemplateDetails.mutateAsync(editData);
								if(res?.status == 200 || res?.status == 201) {
									toast.success('Template details were saved.');
								}
							} catch(err: any) {
								toast.success('There was an issue saving the template.');
								console.log(err);
							}
					}
				}
			}
		} catch(err) {
			console.log('err', err);
		}
	};
	
	function handleSetEditData(e: any, submit?:boolean) {
		const element = e.currentTarget.name;
		const value = e.currentTarget.value;
		const data:any = {
			id: details.id,
			name: details.name.trim(),
			categoryId: details.categoryId,
			description: details.description.trim(),
		}
		data[element] = value;
		setEditData(data);
		submit && handleInputChange();
	}

	useEffect(() => {
		if (details) {
			clearErrors();
			setValue('name', details?.name);
			setValue('categoryId', details?.categoryId ? details?.categoryId.toString() : '');
			setValue('description', details?.description);
		} else {
			reset();
		}
	}, [details, reset]);

	return (
		<div className="relative">
			<div className="w-full flex justify-center py-2">
				<div className={`
						text-center text-lg hover:opacity-80 cursor-pointer mr-2
						${activeTab == 'details' ? 'opacity-100' : 'opacity-50'}
					`}
					onClick={() => setActiveTab('details')}
				>
					Details
				</div>
				<div className={`
						text-center text-lg hover:opacity-80 cursor-pointer ml-2
						${activeTab == 'history' ? 'opacity-100' : 'opacity-50'}
					`}
					onClick={() => setActiveTab('history')}
				>
					History
				</div>
			</div>
			{activeTab == 'details' &&
				<form>
					<div className="mb-1">
						<span className="text-white text-[14px]">Name</span>
						<div className="col-span-2">
							<input
								{...register('name')}
								type="text"
								name="name"
								id="name"
								className="bg-gray-200 p-1.5 w-full rounded pl-2 text-[14px] text-black"
								placeholder="Enter Form Name"
								onChange={async (e) => {
									handleSetEditData(e);
								}}
								onBlur={async () => {
									handleInputChange();
								}}
							/>
							<p className="text-start text-xs text-red-500 font-semibold pt-1">
								{errors.name?.message}
							</p>
						</div>
					</div>
					
					<div className="my-1">
						<span className="text-white text-[14px]">Category</span>
						<div className="col-span-2">
							<select
								{...register('categoryId')}
								name="categoryId"
								id="categoryId"
								className="block w-full p-[7px] rounded bg-gray-200 focus:border-blue-500 text-[14px] text-black"
								onChange={(e) => {
									setSelectFirstChild(true);
									handleSetEditData(e, true);
								}}
							>
								<option className="hidden" value={''}>
									Select Form Category
								</option>
								{formCategories?.data?.map((i:any) => (
									<option value={i?.id} key={i?.id}>
										{i.name.charAt(0).toUpperCase() + i.name.slice(1)}
									</option>
								))}
							</select>
							<p className="text-start text-xs text-red-500 font-semibold pt-1">
								{errors.categoryId?.message}
							</p>
						</div>
					</div>
					
					<div className="mt-1 mb-2">
						<span className="text-white text-[14px]">Description</span>
						<div className="col-span-2">
							<input
								{...register('description')}
								type="text"
								name="description"
								id="description"
								className="bg-gray-200 p-1.5 w-full rounded pl-2 text-[14px] text-black"
								placeholder="Enter Form Description"
								onChange={async (e) => {
									handleSetEditData(e);
								}}
								onBlur={async () => {
									handleInputChange();
								}}
							/>
							<p className="text-start text-xs text-red-500 font-semibold pt-1">
								{errors.description?.message}
							</p>
						</div>
					</div>
				</form>
			}

			{activeTab == 'history' &&
				<div className="mt-2 mb-4">
					<table className="table-auto w-full">
						<thead>
							<tr className="bg-[#00000055]">
								<th className="text-center opacity-70 px-2 py-1">Version</th>
								<th className="text-right opacity-70 px-2 py-1">Last Updated</th>
							</tr>
						</thead>
						<tbody>
							{formTemplateHistory?.data?.map((i:any) => (
								<tr key={i.id}
									className={`
										odd:bg-[#00000033] align-top cursor-pointer hover:ring
										${currentRevisionId == i.id ? 'ring opacity-100' : 'opacity-50 hover:opacity-80'}
									`}
									onClick={(e) => handleFormHistoryRowClick(e, i.id)}
								>
									<td className="text-center text-sm px-2 py-1">{`${i.major}.${i.minor}.${i.revision}`}</td>
									<td className="text-right text-xs flex-wrap break-words px-2 py-1">
										<div className="w-full">{`${i.lastSaved.split('T')[0]} ${i.lastSaved.split('T')[1].split('.')[0]}`}</div>
										<div className="w-full">{`${i.isPublished ? 'Published' : 'Draft'}`}</div>
									</td>
								</tr>
							))}
						</tbody>
						<tfoot>
							<tr className="bg-[#00000055]">
								{formTemplateHistory?.data.length == 1 &&
									<td colSpan={2} className="text-right text-xs opacity-70 px-2 py-1">{formTemplateHistory?.data.length} version</td>
								}
								{formTemplateHistory?.data.length != 1 &&
									<td colSpan={2} className="text-right text-xs opacity-70 px-2 py-1">{formTemplateHistory?.data.length} versions</td>
								}
							</tr>
						</tfoot>
					</table>
				</div>
			}

			{activeTab == 'details' &&
				<div className="flex flex-col">
					<div className="flex gap-2 my-2 justify-center items-center">
						<div>Version:</div>
						<div>{`${details.major}.${details.minor}.${details.revision}`}</div>
						<FormToolButton title="Add Revision" icon={<PlusIcon />} isActive={true} handleClick={createRevision} />
					</div>
					<div className="flex justify-evenly">
						<FormToolButton label="Import" icon={<ImportIcon />} isActive={true} handleClick={importFormJSON} />
						<FormToolButton label="Export" icon={<ExportIcon />} isActive={true} handleClick={exportFormJSON} />
					</div>
					<button
						id="publish_template"
						title="Click to publish tempalte"
						className={`
							text-white font-medium rounded-lg text-sm bg-[#4AA8FE]
							mx-4 py-1 text-center items-center me-2 mb-2
							${publishActive ? ' hover:bg-[#4AA8FE]/75' : 'cursor-default opacity-50 disabled'}
						`}
						onClick={async () => {
							if(publishActive) {
								try {
									const res:any = await publishFormTemplateDefinition.mutateAsync(
										details.definitionId
									);
									if(res?.status == 200 || res?.status == 201) {
										toast.success('Form succesfully published.');
										setPublishActive(false);
									}
								} catch(err: any) {
									toast.success('There was an issue publishing the form.');
									console.log(err);
								}
							}
						}}
					>
						Publish
					</button>
					<button
						id="exit_designer"
						title="Click to exit template designer"
						className="text-white bg-[#29547c]/25 hover:bg-[#29547c]/50 border-[#4AA8FE] border-[1px] hover:border-[#4AA8FE]/75 font-medium rounded-lg text-sm mx-4 py-1 text-center items-center me-2 mb-2"
						onClick={() => {
							navigate(
								params?.mineId
								? `/app/Mines/${params?.mineId}/Forms/Templates`
								: `/app/Forms/Templates`
							);
						}}
					>
						Exit
					</button>
				</div>
			}
		</div>
	);
};