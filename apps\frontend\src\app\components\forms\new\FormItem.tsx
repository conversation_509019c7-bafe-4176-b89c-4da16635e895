import { useEffect, useMemo, useState } from 'react';
import { FormInput, Id, Item } from "../types";
import { useQuery } from '@tanstack/react-query';
import { getActiveShifts } from '../../../../api/shifts/shiftapis';
import { getSections } from '../../../../api/sections/sectionapis';
import Signature from '../../common/Signature';

interface Props {
	itemGroup: string,
	item: Item;
	questionId?: Id,
	saveInputValue: (input: FormInput) => void;
	readOnly: boolean;
	questionIndex: number;
}

interface Style {
	backgroundColor?: string,
	color?: string,
	margin?: string,
	marginTop?: string,
	marginBottom?: string,
	marginLeft?: string,
	marginRight?: string,
	padding?: string,
	paddingTop?: string,
	paddingBottom?: string,
	paddingLeft?: string,
	paddingRight?: string,
	width?: string,
	height?: string,
	fontSize?: string,
}

interface InputStyle {
	height: string,
	width: string,
	minWidth: string,
	minHeight: string,
}

function FormItem({ itemGroup, item, questionId, saveInputValue, readOnly, questionIndex }: Props) {
	const properties = item.properties;
	const key = properties?.key ?? null;
	const placeholder = properties?.placeholder ?? null;
	const text = properties?.text ?? null;
	const typeValue = properties?.type ?? 'Input';
	const required = properties?.requiredfield ?? false;
	const inputTypeOption = properties?.inputoptiontype ?? 'text';
	const dateTypeOption = properties?.dateoptiontype ?? 'date';
	const labelpositiongroup = properties?.labelgroup?.labelposition ?? null;
	const labelposition = labelpositiongroup ?? 'Header';
	const label = properties?.labelgroup?.label ?? properties.label ?? null;
	const bgcolor = properties?.bgcolorgroup?.bgcolor ?? null;
	const textcolor = properties?.colorgroup?.color ?? null;
	const fUnitValueField = properties?.fontsizegroup?.fontsizeunitvaluefield ?? null;
	const fUnitTypeField = properties?.fontsizegroup?.fontsizeunittypefield ?? null;
	const mUnitValueField = properties?.margingroup?.unitvaluefield ?? null;
	const mUnitTypeField = properties?.margingroup?.unittypefield ?? null;
	const mtUnitValueField = properties?.margintop?.unitvaluefield ?? null;
	const mtUnitTypeField = properties?.margintop?.unittypefield ?? null;
	const mbUnitValueField = properties?.marginbottom?.unitvaluefield ?? null;
	const mbUnitTypeField = properties?.marginbottom?.unittypefield ?? null;
	const mlUnitValueField = properties?.marginleft?.unitvaluefield ?? null;
	const mlUnitTypeField = properties?.marginleft?.unittypefield ?? null;
	const mrUnitValueField = properties?.marginright?.unitvaluefield ?? null;
	const mrUnitTypeField = properties?.marginright?.unittypefield ?? null;
	const pUnitValueField = properties?.paddinggroup?.unitvaluefield ?? null;
	const pUnitTypeField = properties?.paddinggroup?.unittypefield ?? null;
	const ptUnitValueField = properties?.paddingtop?.unitvaluefield ?? null;
	const ptUnitTypeField = properties?.paddingtop?.unittypefield ?? null;
	const pbUnitValueField = properties?.paddingbottom?.unitvaluefield ?? null;
	const pbUnitTypeField = properties?.paddingbottom?.unittypefield ?? null;
	const plUnitValueField = properties?.paddingleft?.unitvaluefield ?? null;
	const plUnitTypeField = properties?.paddingleft?.unittypefield ?? null;
	const prUnitValueField = properties?.paddingright?.unitvaluefield ?? null;
	const prUnitTypeField = properties?.paddingright?.unittypefield ?? null;
	const wSizeValueField = properties?.widthgroup?.sizeunitvaluefield ?? null;
	const wSizeUnitTypeField = properties?.widthgroup?.widthunittypefield ?? null;
	const hSizeValueField = properties?.heightgroup?.sizeunitvaluefield ?? null;
	const hSizeUnitTypeField = properties?.heightgroup?.heightunittypefield ?? null;
	const positionValue = properties?.positiongroup ?? 'left';
	const dropdowndatasourcetype = properties?.dropdowndatasourcetype ?? null;
	let dropDownData = [];
	if(dropdowndatasourcetype == 'Custom' && properties?.data) {
		dropDownData = properties?.data;
	}
	
	const [formInputValue, setFormInputValue] = useState<any>();
	const [validField, setValidField] = useState(true);
	const [currentFormInputValue, setCurrentFormInputValue] = useState<any>();
	const [dropDownListData, setDropDownListData] = useState(dropDownData ?? []);
	
	const {
		data: getShiftRes,
		status: apiStatusforShift,
	} = useQuery({
		queryKey: ['active-shifts'],
		queryFn: getActiveShifts,
		refetchOnWindowFocus: false,
		enabled: (typeValue === 'DropDown' && dropdowndatasourcetype === 'Shifts')
	});
	const {
		data: getSectionRes,
		status: apiStatusforSection,
	} = useQuery({
		queryKey: ['all-sections'],
		queryFn: getSections,
		refetchOnWindowFocus: false,
		enabled: (typeValue === 'DropDown' && dropdowndatasourcetype === 'Sections')
	});

	useEffect(() => {
		if(questionId && item.answers) {
			let answer = item.answers[questionId];
			let answerValue = item.answers[questionId];
			if(answer && answer.hasOwnProperty('itemId')) {
				answerValue = answer.itemId;
			}
			setCurrentFormInputValue(answerValue);
			setFormInputValue(answerValue);
		}
		else if(item.answer) {
			let answer = item?.answer ?? '';
			let answerValue = item?.answer ?? '';
			if(answer && answer.hasOwnProperty('itemId')) {
				answerValue = answer.itemId;
			}
			setCurrentFormInputValue(answerValue);
			setFormInputValue(answerValue);
		}
		else {
			setCurrentFormInputValue(null);
			setFormInputValue(null);
		}
		if(item.hasOwnProperty("valid")) {
			setValidField(item.valid ?? true);
		}
		else {
			setValidField(true);
		}
	}, [questionId, item]);
  
	useEffect(() => {
		if(getShiftRes?.data.length > 0 && dropdowndatasourcetype === 'Shifts') {
			setDropDownListData(getShiftRes?.data.map((shift:any) => {
				return {itemId: shift.id, answer:shift.shiftName}
			}));
		}
		if(getSectionRes?.data.length > 0 && dropdowndatasourcetype === 'Sections') {
			setDropDownListData(getSectionRes?.data.map((section:any) => {
				return {itemId: section.id, answer:section.sectionName}
			}));
		}
	}, [getShiftRes?.data, getSectionRes?.data]);
	
	let justifyValue = 'center';
	if(positionValue == 'left') { justifyValue = 'start' }
	if(positionValue == 'right') { justifyValue = 'end' }
	
	const style:Style = {};
	let offset = 0;
	if(!(questionIndex > 0 && labelposition == 'Header') && labelposition !== 'None' && typeValue != 'Header' && typeValue != 'Label') {
		offset += 32
	}
	const inputStyle:InputStyle = {
		height: `calc(100% - ${offset}px)`,
		width: '100%',
		minWidth: '38px',
		minHeight: '38px',
	};
	const radioStyle:InputStyle = {
		height: `calc(100% - ${offset}px)`,
		width: '100%',
		minWidth: '26px',
		minHeight: '26px',
	};
	
	let margin, marginTop, marginBottom, marginLeft, marginRight;
	if(mUnitTypeField) {
		if(mUnitTypeField !== 'individual') {
			margin = `${mUnitValueField ?? ''}${mUnitTypeField ?? ''}`;
		}
		else {
			marginTop = `${mtUnitValueField ?? ''}${mtUnitTypeField ?? ''}`;
			marginBottom = `${mbUnitValueField ?? ''}${mbUnitTypeField ?? ''}`;
			marginLeft = `${mlUnitValueField ?? ''}${mlUnitTypeField ?? ''}`;
			marginRight = `${mrUnitValueField ?? ''}${mrUnitTypeField ?? ''}`;
		}
	}

	let padding, paddingTop, paddingBottom, paddingLeft, paddingRight;
	if(pUnitTypeField) {
		if(pUnitTypeField !== 'individual') {
			padding = `${pUnitValueField ?? ''}${pUnitTypeField ?? ''}`;
		}
		else {
			paddingTop = `${ptUnitValueField ?? ''}${ptUnitTypeField ?? ''}`;
			paddingBottom = `${pbUnitValueField ?? ''}${pbUnitTypeField ?? ''}`;
			paddingLeft = `${plUnitValueField ?? ''}${plUnitTypeField ?? ''}`;
			paddingRight = `${prUnitValueField ?? ''}${prUnitTypeField ?? ''}`;
		}
	}
	
	let width, widthClass;
	if(wSizeUnitTypeField) {
		let type = wSizeUnitTypeField ?? 'w-fit';
		let value = wSizeValueField ?? 0;
		if(type == 'px' || type == '%') {
			width = `${value}${type}`;
		}
		else {
			widthClass = type;
		}
	}
	
	let height, heightClass;
	if(hSizeUnitTypeField) {
		let type = hSizeUnitTypeField ?? 'h-fit';
		let value = hSizeValueField ?? 0;
		if(type == 'px' || type == '%') {
			height = `${value}${type}`;
		}
		else {
			heightClass = type;
		}
	}
	
	let fontSize, fontSizeClass;
	if(fUnitTypeField) {
		let type = fUnitTypeField ?? 'base';
		let value = fUnitValueField ?? 1;
		if(type == 'px' || type == 'rem') {
			fontSize = `${value}${type}`;
		}
		else {
			fontSizeClass = type;
		}
	}
	
	const backgroundColor = `${bgcolor ?? ''}`;
	const color = `${textcolor ?? ''}`;

	if(fontSize && fontSize.length > 0) {style.fontSize = fontSize;}
	if(backgroundColor && backgroundColor.length > 0) {style.backgroundColor = backgroundColor;}
	if(color && color.length > 0) {style.color = color;}
	if(margin && margin.length > 0) {style.margin = margin;}
	if(marginTop && marginTop.length > 0) {style.marginTop = marginTop;}
	if(marginBottom && marginBottom.length > 0) {style.marginBottom = marginBottom;}
	if(marginLeft && marginLeft.length > 0) {style.marginLeft = marginLeft;}
	if(marginRight && marginRight.length > 0) {style.marginRight = marginRight;}
	if(padding && padding.length > 0) {style.padding = padding;}
	if(paddingTop && paddingTop.length > 0) {style.paddingTop = paddingTop;}
	if(paddingBottom && paddingBottom.length > 0) {style.paddingBottom = paddingBottom;}
	if(paddingLeft && paddingLeft.length > 0) {style.paddingLeft = paddingLeft;}
	if(paddingRight && paddingRight.length > 0) {style.paddingRight = paddingRight;}
	if(width && width.length > 0) {style.width = width;}
	if(height && height.length > 0) {style.height = height;}
	
	function inputValueChanged(e:any) {
		if(!readOnly) {
			let newValue = e.target.value;
			setValidField(true);
			setCurrentFormInputValue(newValue);
		}
	}

	function handleInputChange(e:any) {
		if(!readOnly) {
			let newValue = e.target.value;
			let name = e.target.name;

			if(typeValue == 'Radio') {
				const radioGroup = document.getElementsByName(name);
				
				radioGroup.forEach((radio:any) => {
					if(radio.checked) {
						newValue = radio.value;
					}
				});
			}
			if(typeValue == 'Checkbox') {
				newValue = null;
				if(e.target.checked) {
					newValue = e.target.value;
				}
			}
			if(typeValue == 'DropDown') {
				newValue = {
					itemId: e.target.value,
					answer: e.target.options[e.target.selectedIndex].text
				};
			}
			if(newValue !== formInputValue) {
				const inputToSave: FormInput = {
					item,
					value: newValue,
				};
				if(questionId) {
					inputToSave.questionId = questionId;
				}
				setValidField(true);
				saveInputValue(inputToSave);
			}
		}
	};

	function saveSignatureField(newValue:string) {
		if(!readOnly) {
			if(newValue !== formInputValue) {
				const inputToSave: FormInput = {
					item,
					value: newValue,
				};
				if(questionId) {
					inputToSave.questionId = questionId;
				}
				setValidField(true);
				saveInputValue(inputToSave);
			}
		}
	}
	
	return (
		<div
			style={style}
			data-refkey={key ?? ''}
			className={`
				relative p-2 flex self-start flex-wrap
				${widthClass ?? ''} ${heightClass ?? ''}
				text-${fontSizeClass ?? ''}
				justify-${justifyValue ?? 'center'}
				text-${positionValue ?? 'center'}
				justify-${positionValue ?? 'center'}
				 ${readOnly ? 'opacity-70' : ''}
			`}
		>
			
			{!(questionIndex > 0 && labelposition == 'Header') && labelposition !== 'None' && typeValue != 'Header' && typeValue != 'Label' && (
				<label className={`w-full`}>{label}
					{required &&
						<span className="text-red-600 font-medium ml-[1px]">*</span>
					}
				</label>
			)}
			{typeValue == 'Checkbox' && (
				<input
					style={radioStyle}
					disabled={readOnly}
					checked={currentFormInputValue === label}
					name={itemGroup}
					className={`form-check-input ${labelposition}`}
					type='checkbox'
					value={label}
					onChange={(e) => handleInputChange(e)}
				/>
			)}
			{typeValue == 'Color' && (
				<div className='color-element-container flex flex-col items-center gap-2'>
					<input
						style={inputStyle}
						disabled={readOnly}
						value={currentFormInputValue ?? '#ffffff'}
						className={`color-element ${labelposition}`}
						type='color'
						onChange={(e) => inputValueChanged(e)}
						onBlur={(e) => handleInputChange(e)}
						onKeyDown={(e) => {
							e.key === 'Enter' && handleInputChange(e);
							if(e.key === 'Escape') {
								setCurrentFormInputValue(formInputValue);
							}
						}}
					/>
				</div>
			)}
			{typeValue == 'Date' && (
				<input
					style={inputStyle}
					disabled={readOnly}
					value={currentFormInputValue ?? ''}
					className={`bg-gray-200 p-1.5 w-full rounded text-black ${labelposition}`}
					type={dateTypeOption}
					onChange={(e) => handleInputChange(e)}
					onBlur={(e) => handleInputChange(e)}
					onKeyDown={(e) => {
						e.key === 'Enter' && handleInputChange(e);
						if(e.key === 'Escape') {
							setCurrentFormInputValue(formInputValue);
						}
					}}
				/>
			)}
			{typeValue == 'DropDown' && (
				<select
					defaultValue={-1}
					style={inputStyle}
					disabled={readOnly}
					value={currentFormInputValue ?? undefined}
					className={`block w-full p-[7px] rounded bg-gray-200 focus:border-blue-500 text-black ${labelposition}`}
					onChange={(e) => handleInputChange(e)}
				>
					<option disabled value={-1} className="text-gray-300 bg-gray-500">Select an option</option>
					{dropDownListData.map((o:any) => <option value={o.itemId} key={o.itemId} label={o.answer}>{o.answer}</option>)}
				</select>
			)}
			{typeValue == 'Header' && (
				<h1>{text ?? ''}</h1>
			)}
			{typeValue == 'Input' && (
				<input
					style={inputStyle}
					disabled={readOnly}
					value={currentFormInputValue ?? ''}
					className={`bg-gray-200 p-1.5 w-full rounded text-black ${labelposition}`}
					type={inputTypeOption}
					placeholder={readOnly ? '' : placeholder ?? null}
					onChange={(e) => inputValueChanged(e)}
					onBlur={(e) => handleInputChange(e)}
					onKeyDown={(e) => {
						e.key === 'Enter' && handleInputChange(e);
						if(e.key === 'Escape') {
							setCurrentFormInputValue(formInputValue);
						}
					}}
				/>
			)}
			{typeValue == 'Label' && (
				<label>{text ?? ''}</label>
			)}
			{typeValue == 'Radio' && (
				<input
					style={radioStyle}
					value={label}
					disabled={readOnly}
					checked={currentFormInputValue === label}
					name={itemGroup}
					className={`form-check-input ${labelposition}`} type='radio'
					onChange={(e) => handleInputChange(e)}
				/>
			)}
			{typeValue == 'Signature' && (
				<Signature
					readOnly={readOnly}
					currentValue={currentFormInputValue ?? ''}
					saveSignatureField={saveSignatureField}
					style={inputStyle}
				/>
			)}
			{typeValue == 'Textarea' && (
				<textarea
					style={inputStyle}
					className={`bg-gray-200 p-1.5 w-full rounded text-black min-h-[100px] ${labelposition}`}
					placeholder={readOnly ? '' : placeholder ?? null}
					disabled={readOnly}
					value={currentFormInputValue ?? ''}
					onChange={(e) => inputValueChanged(e)}
					onBlur={(e) => handleInputChange(e)}
					onKeyDown={(e) => {
						e.key === 'Enter' && handleInputChange(e);
						if(e.key === 'Escape') {
							setCurrentFormInputValue(formInputValue);
						}
					}}
				></textarea>
			)}
			{!validField &&
				<p className="text-start text-xs text-red-500 font-semibold pt-1">
					This field is required
				</p>
			}
		</div>
	)
}

export default FormItem;