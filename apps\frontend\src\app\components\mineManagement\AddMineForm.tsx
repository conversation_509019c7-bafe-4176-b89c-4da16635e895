import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import SelectWithSearch from '../common/SelectWithSearch';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import mixpanel from 'mixpanel-browser';

import {
  useAddCompany,
  useEditCompany,
  useCheckCompanyName,
  useGetMine,
} from '../../../services/mutations/companymutations';
import {
  useFindByEmail,
  useFindByUsername,
  useGenerateUsername,
} from '../../../services/mutations/usermutations';
import { getComplianceAllMinesData } from '../../../api/compliance/complianceapis';
import { toast } from 'react-toastify';
import { CloseIcon, PlusIcon, MinusIcon } from '../../../assets/icons/icons';
import decodeJWT from '../../../utils/jwtdecoder';
import { emailRegEx } from '../../../utils/constant';
import { useQuery } from '@tanstack/react-query';
import { getTimezoneData } from '../../../api/mines/mineapis';
import Modal from '../common/Modal';
import { getPageNamesFromUrl } from '../PageName';
import { useParams } from 'react-router-dom';

const schema = yup.object({
  mshaId: yup
    .string()
    .transform((value) => value.trim())
    .max(50, 'MSHA ID must be less than 50 characters')
    .required('Please Select MSHA ID from the list'),
  mineName: yup
    .string()
    .transform((value) => value.trim())
    .max(200, 'Mine name should not exceed 200 characters.')
    .required('Please enter a mine name'),
  companyName: yup
    .string()
    .transform((value) => value.trim())
    .max(200, 'Company Name should not exceed 200 characters.')
    .required('Please enter a company name'),
  location: yup
    .string()
    .transform((value) => value.trim())
    .max(50, 'Location should not exceed 50 characters.')
    .required('Please enter a location'),
  timezone: yup.number().required('Please select Time zone'),
  adminFormIsVisible: yup.boolean(),
  firstName: yup
    .string()
    .transform((value) => value.trim())
    .when('adminFormIsVisible', ([adminFormIsVisible], schema) => {
      return adminFormIsVisible
        ? schema
            .required('Please enter first name')
            .max(20, 'First name should not exceed 20 characters')
        : schema;
    }),
  lastName: yup
    .string()
    .transform((value) => value.trim())
    .when('adminFormIsVisible', ([adminFormIsVisible], schema) => {
      return adminFormIsVisible
        ? schema
            .required('Please enter last name')
            .max(20, 'Last name should not exceed 20 characters')
        : schema;
    }),
  emailAddress: yup
    .string()
    .transform((value) => value.trim())
    .when('adminFormIsVisible', ([adminFormIsVisible], schema) => {
      return adminFormIsVisible
        ? schema
            .transform((value) => value.trim())
            .email('Invalid email address')
            .required('Please enter email address')
        : schema;
    }),
  confirmEmailAddress: yup
    .string()
    .transform((value) => value.trim())
    .when('adminFormIsVisible', ([adminFormIsVisible], schema) => {
      return adminFormIsVisible
        ? schema
            .transform((value) => value.trim())
            .email('Invalid email address')
            .required('Please confirm email address')
        : schema;
    }),
  username: yup
    .string()
    .transform((value) => value.trim())
    .when('adminFormIsVisible', ([adminFormIsVisible], schema) => {
      return adminFormIsVisible
        ? schema
            .transform((value) => value.trim())
            .max(25, 'Username name should not exceed 25 characters.')
            .required('Please enter username')
        : schema;
    }),
});

export default function AddMineForm(props: any) {
  let { openAddMineForm, setOpenAddMineForm } = props;
  const {
    register,
    unregister,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
    getValues,
    setError,
    clearErrors,
  } = useForm({
    resolver: yupResolver(schema),
  });

  const createdBy = decodeJWT()?.userId;
  const addCompany = useAddCompany();
  const editCompany = useEditCompany();
  const generateUsername = useGenerateUsername();
  const checkCompanyName = useCheckCompanyName();
  const getMine = useGetMine();
  const getUser = useFindByUsername();
  const checkEmailMutation = useFindByEmail();

  const [selectedMineById, setSelectMineById] = useState('');
  const [openAddAdminForm, setOpenAddAdminForm] = useState(false);
  const [complianceMines, setComplianceMines] = useState<any[]>([]);
  const [inputTextTimezone, setInputTextTimezone] = useState('');
  const [filteredTimezones, setFilteredTimezones] = useState([]);
  const [timeZoneInput, setTimeZoneInput] = useState(0);
  const [timezoneName, setTimezoneName] = useState('');
  const [openTimezoneModal, setOpenTimezoneModal] = useState(false);
  const params = useParams();
  const url = getPageNamesFromUrl(params['*']);

  const { data: timezoneData } = useQuery({
    queryKey: ['timezone'],
    queryFn: getTimezoneData,
    refetchOnWindowFocus: false,
  });
  const [mshaIdFound, setMshaIdFound] = useState(false);

  useEffect(() => {
    if (inputTextTimezone) {
      const filtered = timezoneData?.data.filter((timezone: any) =>
        timezone.name.toLowerCase().includes(inputTextTimezone.toLowerCase())
      );
      clearErrors('timezone');
      setFilteredTimezones(filtered);
    } else {
      setFilteredTimezones([]);
    }
  }, [inputTextTimezone, timezoneData, clearErrors]);

  useEffect(() => {
    if (!props?.editData) {
      const mshaId = String(getValues('mshaId')).trim();
      if (mshaId.length > 0 && mshaId !== selectedMineById) {
        const selectedMine = complianceMines?.filter(
          (ele: any) => ele?.mineId === mshaId
        )[0];

        clearErrors('mshaId');
        clearErrors('mineName');
        clearErrors('companyName');
        clearErrors('location');
        setValue('mineName', selectedMine?.mineName);
        setValue('companyName', selectedMine?.companyName);
        setValue('location', selectedMine?.state);
      }
      setSelectMineById(mshaId);
    }
  }, [watch('mshaId')]);

  useEffect(() => {
    const formVisible = getValues('adminFormIsVisible');
    if (formVisible) {
      //
    } else {
      clearErrors('firstName');
      unregister('firstName');
      clearErrors('lastName');
      unregister('lastName');
      clearErrors('emailAddress');
      unregister('emailAddress');
      clearErrors('confirmEmailAddress');
      unregister('confirmEmailAddress');
      clearErrors('username');
      unregister('username');
    }
  }, [watch('adminFormIsVisible')]);

  useEffect(() => {
    if (openAddAdminForm) {
      if (
        String(getValues('firstName')).trim() !== '' ||
        String(getValues('lastName')).trim() !== ''
      ) {
        const data = {
          firstname: String(getValues('firstName')).trim(),
          lastname: String(getValues('lastName')).trim(),
        };
        try {
          const res = async () => {
            const response = await generateUsername.mutateAsync(data);
            if (response?.data !== 'uundefined' && response.data !== 404) {
              setValue('username', response?.data);
              clearErrors('username');
            } else {
              if (String(getValues('firstName')).trim() == '') {
                setValue('username', '');
              } else {
                setValue('username', String(getValues('firstName')).charAt(0));
              }
            }
          };
          if (String(getValues('lastName')).trim() !== '') {
            res();
          } else {
            if (String(getValues('firstName')).trim() == '') {
              setValue('username', '');
            } else {
              setValue('username', String(getValues('firstName')).charAt(0));
            }
          }
        } catch (err: any) {
          if (String(getValues('firstName')).trim() == '') {
            setValue('username', '');
          } else {
            setValue('username', String(getValues('firstName')).charAt(0));
          }
        }
      }
      if (String(getValues('firstName')).trim() == '') {
        setValue('username', '');
      }
    }
  }, [watch('firstName'), watch('lastName')]);

  const handleSearchComplianceMines = async (partialId: string) => {
    const res = await getComplianceAllMinesData(partialId);
    setComplianceMines(res?.data ?? []);
  };

  const onSubmitHandler = async (data: any) => {
    let _firstName = '',
      _lastName = '',
      _username = '',
      _emailAddress = '';

    const _mshaId = String(getValues('mshaId')).trim();
    const _mineName = String(getValues('mineName')).trim();
    const _companyName = String(getValues('companyName')).trim();
    const _location = String(getValues('location')).trim();
    const _timezone = data.timezone;

    let validTimezone = timezoneData?.data?.some(
      (option: any) => option.name === inputTextTimezone
    );

    if (!validTimezone) {
      setError('timezone', {
        type: 'manual',
        message: 'Please select valid timezone',
      });
      return;
    } else {
      clearErrors('timezone');
    }

    if (openAddAdminForm) {
      try {
        _firstName = String(getValues('firstName')).trim();
        _lastName = String(getValues('lastName')).trim();
        _username = String(getValues('username')).trim();
        _emailAddress = String(getValues('emailAddress')).trim();
      } catch (error: any) {
        toast.error(error.response?.data?.message);
        return;
      }

      if (!emailRegEx.test(data.emailAddress)) {
        setError('emailAddress', {
          type: 'manual',
          message: 'Please enter valid email',
        });
        return;
      }

      if (data.emailAddress !== data?.confirmEmailAddress) {
        setError('confirmEmailAddress', {
          type: 'manual',
          message: 'Email addresses does not match',
        });
        return;
      }

      if (
        _firstName === '' ||
        _lastName === '' ||
        _username === '' ||
        _emailAddress === '' ||
        _username.includes(' ')
      ) {
        if (_username.includes(' ')) {
          setError('username', {
            type: 'manual',
            message: 'No spacing is allowed in a username',
          });
        } else {
          toast.error('Please enter valid information');
        }
        return;
      }

      data.addAdminUser = true;
      data.firstName = _firstName;
      data.lastName = _lastName;
      data.username = _username;
      data.email = _emailAddress;
      data.createdBy = createdBy;

      const userRes = await getUser.mutateAsync(data?.username);

      if (userRes?.data?.id) {
        setError('username', {
          type: 'manual',
          message: 'Username already exist',
        });
        return;
      } else {
        clearErrors('username');
      }

      const responseEmail = await checkEmailMutation.mutateAsync(_emailAddress);

      if (responseEmail?.data?.id) {
        setError('emailAddress', {
          type: 'manual',
          message: 'User already exists with this email address',
        });
        return;
      } else {
        clearErrors('emailAddress');
      }
    }

    data.mineCode = _mshaId;
    data.mineLocation = _location;
    data.timezoneId = _timezone;

    if (
      _mshaId === '' ||
      _mineName === '' ||
      _companyName === '' ||
      _location === '' ||
      _timezone === null
    ) {
      toast.error('Please enter valid information');
      return;
    }

    const companyRes = await checkCompanyName.mutateAsync({
      name: data?.companyName,
      code: data?.companyName,
    });

    if (
      companyRes?.data?.id &&
      companyRes?.data?.id !== props?.editData?.companyId
    ) {
      setError('companyName', {
        type: 'manual',
        message: 'Company name already exist',
      });
      return;
    } else {
      clearErrors('companyName');
      unregister('companyName');
    }

    const mineRes = await getMine.mutateAsync({
      name: data?.mineName,
      code: data?.mineCode,
    });

    if (mineRes?.data?.id && mineRes?.data?.id !== props?.editData?.id) {
      if (mineRes?.data?.duplicate === 'name') {
        setError('mineName', {
          type: 'manual',
          message: 'Mine name already exist',
        });
        return;
      } else {
        clearErrors('mineName');
      }

      if (mineRes?.data?.duplicate === 'code') {
        setError('mshaId', {
          type: 'manual',
          message: 'MSHA ID already exist',
        });
        return;
      } else {
        clearErrors('mshaId');
      }
    }

    if (props?.editData) {
      data.id = props?.editData?.id;
      if (inputTextTimezone === '') {
        setError('timezone', {
          type: 'manual',
          message: 'Please Select Time zone',
        });
        return;
      }
      if (_companyName === '' && inputTextTimezone === '') {
        setError('companyName', {
          type: 'manual',
          message: 'Please enter a company name',
        });
        setError('timezone', {
          type: 'manual',
          message: 'Please Select Time zone',
        });
        return;
      }
      if (_mineName === '') {
        setError('mineName', {
          type: 'manual',
          message: 'Please enter a mine name',
        });
        return;
      }
      if (_location === '') {
        setError('location', {
          type: 'manual',
          message: 'Please enter a location',
        });
        return;
      }
      try {
        await editCompany.mutateAsync(data);
        mixpanel.track('Update Mine', {
          MshaId: data?.mshaId,
          Mine_Name: data?.mineName,
        });
        toast.success('You have successfully edited the mine');
        setOpenAddMineForm(false);
        setTimeout(() => {
          reset();
        }, 800);
      } catch (err: any) {
        console.error('Failed to edit form:', err.message);
        mixpanel.track('Error Event', {
          Page_Name: url,
          Action_Name: 'Update Mine',
        });
        toast.error(err.message);
      }
    }

    const isValid = Object.keys(errors).length === 0;
    if (isValid) {
      setOpenTimezoneModal(true);
    }
  };

  const handleYesButtonClick = async () => {
    try {
      const formData: any = getValues();
      const newMine = await addCompany.mutateAsync(formData);
      mixpanel.track('Add Mine', {
        MshaId: newMine.data.mshaId,
        Mine_Name: newMine.data.mineName,
      });
      toast.success('Mine created successfully');
      setOpenTimezoneModal(false);
      setOpenAddMineForm(false);
      setTimeout(() => {
        reset();
      }, 800);
    } catch (error: any) {
      console.error('Failed to submit form:', error.message);
      mixpanel.track('Error Event', {
        Page_Name: url,
        Action_Name: 'Add Mine',
      });
      toast.error(error.message);
    }
  };

  useEffect(() => {
    if (props.editData) {
      clearErrors();
      setValue('mshaId', props?.editData?.code);
      setValue('mineName', props?.editData?.name);
      setValue('companyName', props?.editData?.company?.name);
      setValue('location', props?.editData?.location);
      setValue('timezone', props?.editData?.timezoneId);
      setInputTextTimezone(
        timezoneData?.data?.find(
          (tz: any) => tz?.id === props?.editData?.timezoneId
        )?.name || ''
      );
      setTimeZoneInput(props?.editData?.timezoneId || '');
    } else {
      reset();
    }
  }, [props?.editData, reset]);

  useEffect(() => {
    if (props.editData) {
      setValue('timezone', props.editData?.timezoneId);
      setInputTextTimezone(
        timezoneData?.data?.find(
          (tz: any) => tz.id === props.editData?.timezoneId
        )?.name || ''
      );
    }
    if (props.editData && props.editData.timezoneId) {
      const timezone = timezoneData?.data.find(
        (zone: any) => zone.id === props.editData.timezoneId
      );
      setTimezoneName(timezone?.name || '');
    }
  }, [props.editData, timezoneData, setValue]);

  const handleOptionClick = async (data: any) => {
    setMshaIdFound(true);
    setValue('mshaId', data?.mineId);
    setValue('timezone', timeZoneInput);
  };

  const handleClickTimezone = async (data: any) => {
    setValue('timezone', data?.id || null);
    setInputTextTimezone(data?.name || '');
    setTimeZoneInput(data?.id || '');
    setTimezoneName(data?.name || '');
  };

  const handleSearchTimezone = (query: string) => {
    if (!timezoneData?.data) return;
    const filtered = timezoneData?.data?.filter((timezone: any) =>
      timezone.name.toLowerCase().includes(query.toLowerCase())
    );
    setFilteredTimezones(filtered);
  };

  const handleInputChange = (value: string) => {
    setInputTextTimezone(value);
    // validateValue(value);
  };

  const notFoundOption = async () => {
    setMshaIdFound(false);
    reset();
  };

  const handleConfirmDelete = async () => {
    setOpenTimezoneModal(false);
  };

  const handleCloseModal = () => {
    setOpenTimezoneModal(false);
  };

  return (
    <div className="flex flex-col ">
      <div className="flex-grow ">
        <div className="flex justify-between mx-4">
          <div className="block">
            <h6 className="font-semibold text-[24px] text-white">
              {props?.editData ? 'Edit Mine' : 'Add New Mine'}
            </h6>
            <p className="font-normal my-0 text-[16px] tracking-normal leading-5 text-[#cccccc] ">
              {!props?.editData
                ? 'Enter all of the required information to add a new mine'
                : ``}
            </p>
          </div>
          <div
            className="mt-1 cursor-pointer"
            onClick={() => {
              setOpenAddMineForm(false);
            }}
          >
            <CloseIcon />
          </div>
        </div>
        <div className="">
          <form onSubmit={handleSubmit(onSubmitHandler)}>
            <div className="rounded pt-3 font-normal">
              <div className="m-2">
                <div className="grid grid-cols-2 gap-y-3">
                  <div className="mx-2">
                    <span className="text-white text-[16px] py-1">
                      MSHA ID
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    {!props?.editData ? (
                      <SelectWithSearch
                        searchText={props?.editData?.code ?? ''}
                        dataKey={'mineId'}
                        data={complianceMines}
                        searchAction={handleSearchComplianceMines}
                        actionKeyLimit={4}
                        optionClick={handleOptionClick}
                        notFoundOption={notFoundOption}
                      />
                    ) : (
                      ''
                    )}

                    <input
                      {...register('mshaId')}
                      type="text"
                      name="mshaId"
                      id="mshaId"
                      placeholder="Select MSHA ID"
                      className={`
                        'bg-gray-200 p-1.5 w-full my-1 rounded pl-2 text-[16px]'
                        ${
                          !props?.editData
                            ? 'text-gray-400 hidden'
                            : 'text-black'
                        }
                      `}
                      disabled={props?.editData ? true : false}
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.mshaId?.message}
                    </p>
                  </div>
                  <div className="mx-2">
                    <span className="text-white text-[16px]">
                      Mine Name
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('mineName')}
                      type="text"
                      name="mineName"
                      id="mineName"
                      className="bg-gray-200 p-1.5 my-1 w-full rounded pl-2 text-[16px] text-black"
                      placeholder="Enter Mine Name"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.mineName?.message}
                    </p>
                  </div>
                  <div className="mx-2">
                    <span className="text-white text-[16px]">
                      Company
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('companyName')}
                      type="text"
                      name="companyName"
                      id="companyName"
                      className="bg-gray-200 my-1 p-1.5 w-full rounded pl-2 text-[16px] text-black"
                      placeholder="Enter Company"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.companyName?.message}
                    </p>
                  </div>
                  <div className="mx-2">
                    <span className="text-white text-[16px]">
                      Location
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    <input
                      {...register('location')}
                      type="text"
                      name="location"
                      id="location"
                      className="bg-gray-200 my-1 p-1.5 w-full rounded pl-2 text-[16px] text-black"
                      placeholder="Enter Location"
                    />
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.location?.message}
                    </p>
                  </div>

                  <div className="mx-2">
                    <span className="text-white text-[16px] py-1">
                      Time zone
                      <span className="text-red-600 font-medium ml-[1px]">
                        *
                      </span>
                    </span>
                    {!props?.editData ? (
                      <SelectWithSearch
                        searchText={inputTextTimezone}
                        dataKey={'timezone'}
                        data={timezoneData?.data}
                        searchAction={handleSearchTimezone}
                        actionKeyLimit={4}
                        optionClick={handleClickTimezone}
                        placeholder="Select Time zone"
                        onInputChange={handleInputChange}
                        maxInputLength={50}
                      />
                    ) : (
                      ''
                    )}
                    {props?.editData ? (
                      <input
                        {...register('timezone')}
                        type="text"
                        name="timezone"
                        id="timezone"
                        className={`
                        ' p-1.5 w-full my-1 rounded pl-2 text-[16px]'
                        ${props?.editData ? 'cursor: not-allowed' : ''}
                      `}
                        value={timezoneName}
                        disabled={props?.editData ? true : false}
                      />
                    ) : (
                      ''
                    )}
                    <p className="text-start text-xs text-red-500 font-semibold pt-1">
                      {errors.timezone?.message}
                    </p>
                    {!props?.editData ? (
                      <p className="text-sm text-[#FFB132]">
                        {
                          'Note: Time zone cannot be changed after mine creation.'
                        }
                      </p>
                    ) : (
                      ''
                    )}

                    {openTimezoneModal ? (
                      <Modal
                        size="lg"
                        Content={
                          <div className="p-4 ">
                            <div className="text-[24px] text-white text-center ">
                              <p> {`You have selected the time zone`}</p>
                              <p>{`"${timezoneName}"`} </p>
                              {`Do you wish to proceed?`}
                            </div>
                            <div className="mt-2 text-center">
                              <button
                                onClick={() =>
                                  setOpenTimezoneModal(!openTimezoneModal)
                                }
                                title="click to cancel."
                                id="clearWatchlistButton"
                                className="  hover:border-[#4AA8FE]/75 text-white text-[14px] py-2 px-8 rounded-lg focus:outline-none focus:shadow-outline border border-[#4AA8FE] mr-2"
                              >
                                Cancel
                              </button>

                              <button
                                onClick={handleYesButtonClick}
                                title="click to add new mine information."
                                className="bg-[#4AA8FE] hover:border-[#4AA8FE]/75 hover:bg-[#4AA8FE]/75 text-white text-[14px] py-2 px-8 rounded-lg focus:outline-none focus:shadow-outline border border-[#4AA8FE]"
                              >
                                Yes
                              </button>
                            </div>
                          </div>
                        }
                        backBg="bg-[#1D4465] border-[#1D4465] rounded-lg"
                        alignment="flex items-center justify-center"
                      />
                    ) : (
                      <></>
                    )}
                  </div>
                </div>
              </div>
              {!props?.editData ? (
                <div className="grid grid-cols-1 p-1 m-2">
                  <div className="text-white bg-transparent border-[#4AA8FE] border-[1px] hover:border-[#4AA8FE]/75 font-normal rounded-lg text-sm p-2 w-full ml-1 mr-2 h-auto">
                    <div className="grid grid-cols-2 p-1">
                      <div className="mx-2 text-left align-middle">
                        <span className="text-white text-[16px] ">Admin</span>
                      </div>
                      <div className="hidden">
                        <input
                          {...register('adminFormIsVisible')}
                          type="checkbox"
                          name="adminFormIsVisible"
                          id="adminFormIsVisible"
                        />
                      </div>
                      <div className="mx-2 text-right">
                        {!openAddAdminForm ? (
                          <button
                            id="add_admin_button"
                            title="Click to add admin"
                            onClick={(e) => {
                              e.preventDefault();
                              setValue('adminFormIsVisible', true);
                              setOpenAddAdminForm(true);
                            }}
                            className="text-[#4AA8FE] bg-transparent hover:text-[#4AA8FE]/75 rounded-lg inline-flex align-middle"
                          >
                            <div className="px-1">
                              <PlusIcon stroke={'#4AA8FE'} />
                            </div>
                            <span className=" text-[14px]">Add</span>
                          </button>
                        ) : (
                          <button
                            id="remove_admin_button"
                            title="Click to remove"
                            onClick={(e) => {
                              e.preventDefault();
                              setValue('adminFormIsVisible', false);
                              setOpenAddAdminForm(false);
                            }}
                            className="text-[#4AA8FE] bg-transparent hover:text-[#4AA8FE]/75 rounded-lg inline-flex align-middle"
                          >
                            <div className="px-1">
                              <MinusIcon stroke={'#4AA8FE'} />
                            </div>
                            <span className=" text-[14px]">Remove</span>
                          </button>
                        )}
                      </div>
                      {openAddAdminForm ? (
                        <>
                          <div className="mx-2 pt-4">
                            <span className="text-white text-[16px]">
                              First Name
                              <span className="text-red-600 font-medium ml-[1px]">
                                *
                              </span>
                            </span>
                            <input
                              {...register('firstName')}
                              type="text"
                              name="firstName"
                              id="firstName"
                              className="bg-gray-200 my-1 p-1.5 w-full rounded pl-2 text-[16px] text-black"
                              placeholder="Enter First Name"
                            />
                            <p className="text-start text-xs text-red-500 font-semibold pt-1">
                              {errors.firstName?.message}
                            </p>
                          </div>
                          <div className="mx-2 pt-4">
                            <span className="text-white text-[16px]">
                              Last Name
                              <span className="text-red-600 font-medium ml-[1px]">
                                *
                              </span>
                            </span>
                            <input
                              {...register('lastName')}
                              type="text"
                              name="lastName"
                              id="lastName"
                              className="bg-gray-200 p-1.5 my-1 w-full rounded pl-2 text-[16px] text-black"
                              placeholder="Enter Last Name"
                            />
                            <p className="text-start text-xs text-red-500 font-semibold pt-1">
                              {errors.lastName?.message}
                            </p>
                          </div>
                          <div className="mx-2 pt-4">
                            <span className="text-white text-[16px]">
                              Email Address
                            </span>
                            <span className="text-red-600 font-normal ml-[1px]">
                              *
                            </span>
                            <input
                              {...register('emailAddress')}
                              type="text"
                              name="emailAddress"
                              id="emailAddress"
                              className="bg-gray-200 p-1.5 w-full my-1 rounded pl-2 text-[16px] text-black"
                              placeholder="Enter Email Address"
                            />
                            <p className="text-start text-xs text-red-500 font-semibold pt-1">
                              {errors.emailAddress && (
                                <span>{errors.emailAddress.message}</span>
                              )}
                            </p>
                          </div>
                          <div className="mx-2 pt-4">
                            <span className="text-white text-[16px]">
                              Confirm Email Address
                            </span>
                            <span className="text-red-600 font-medium ml-[1px]">
                              *
                            </span>
                            <input
                              {...register('confirmEmailAddress')}
                              type="text"
                              name="confirmEmailAddress"
                              id="confirmEmailAddress"
                              className="bg-gray-200 p-1.5 w-full my-1 rounded pl-2 text-[16px] text-black"
                              placeholder="Confirm Email Address"
                            />
                            <p className="text-start text-xs text-red-500 font-semibold pt-1">
                              {errors.confirmEmailAddress?.message}
                            </p>
                          </div>
                          <div className="mx-2 col-start-1 col-end-3 pt-4">
                            <span className="text-[16px] text-white">
                              Username (auto-generated)
                            </span>
                            <span className="text-red-600 font-medium ml-[1px]">
                              *
                            </span>
                            <input
                              {...register('username')}
                              type="text"
                              name="username"
                              id="username"
                              className="bg-gray-200 p-1.5 w-full my-1 rounded pl-2 text-[16px] text-black"
                              placeholder="Enter Username"
                            />
                            <p className="text-start text-xs text-red-500 font-semibold pt-1">
                              {errors.username?.message}
                            </p>
                          </div>
                        </>
                      ) : (
                        ''
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                ''
              )}

              <div className="grid grid-cols-2 mt-5">
                <div className="mx-3">
                  <button
                    id="cancel_button"
                    title="Click to cancel"
                    type="button"
                    onClick={() => {
                      setOpenAddMineForm(false);
                    }}
                    className={`text-white bg-transparent border-[#4AA8FE] border-[1px] hover:border-[#4AA8FE]/75 font-medium rounded-lg text-sm px-10 py-2 text-center h-9 w-full items-center  me-2 mb-2 ${
                      props?.editData ? 'px-6' : 'px-4'
                    }`}
                  >
                    {'Cancel'}
                  </button>
                </div>
                <div className="mx-3">
                  <button
                    id="create_mine"
                    title={
                      !props?.editData
                        ? 'Click to add new mine information'
                        : 'Click to save mine information'
                    }
                    type="submit"
                    className={`text-white bg-[#4AA8FE]  hover:bg-[#4AA8FE]/75 font-medium rounded-lg text-sm px-8 py-2 text-center h-9 w-full items-center  me-2 mb-2 ${
                      props?.editData ? 'px-6' : 'px-4'
                    }`}
                  >
                    {props?.editData ? 'Save' : 'Add'}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
