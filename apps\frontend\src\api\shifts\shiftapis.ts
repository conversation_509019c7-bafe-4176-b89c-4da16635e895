import { ApiClient } from '../apiClient';

export interface ShiftData {
  id?: number;
  mineId?: number;
  shiftName?: string;
  shift_type?: string;
  startTime?: string;
  endTime?: string;
  isActive?: number;
}

export const checkShiftNameMutation = async (name: string) => {
  const encodedName = encodeURIComponent(name);
  return await ApiClient.get(`/api/portal/v1/shifts/name/${encodedName}`);
}

export const addShift = async (data: ShiftData) => {
  return await ApiClient.post(`/api/portal/v1/shifts`, data);
};

export const editShift = async (id: number, data: ShiftData) => {
  return await ApiClient.patch(`/api/portal/v1/shifts/${id}`, data);
};

export const getShifts = async () => {
  return await ApiClient.get(`/api/portal/v1/shifts`);
};

export const getAllShifts = async () => {
  return await ApiClient.get('/api/portal/v1/shifts/all-shifts');
};

export const getActiveShifts = async () => {
  return await ApiClient.get('/api/portal/v1/shifts/active-shifts');
};

export const updateShiftStatus = async (id: number) => {
  return await ApiClient.patch(`/api/portal/v1/shifts/isActive/${id}`);
};
