export type Id = string | number;

export type Tool = {
	id: Id,
	label: string,
}

export type FormInput = {
	item: Item,
	value: any,
	questionId?: Id,
	properties?: any,
}

export type Item = {
	id: Id,
	key: Id,
	index: number,
	groupId: Id,
	content: string,
	properties: any,
	answers?: any,
	answer?: any,
	valid?: boolean,
}

export type Question = {
	id: Id,
	index: number,
	groupId: Id,
	name: string
}

export type Answer = {
	key: Id,
	value: Id
}

export type Group = {
	id: Id,
	index: number,
	name: string,
	items: Item[],
	properties: any,
	questions: Question[]
}

export type Category = {
	id: Id,
	name: string,
}

export type TemplateDetails = {
	id: Id,
	definitionId: Id,
	major: Id,
	minor: Id,
	revision: Id,
	categoryId: Id,
	name: string,
	category: Category,
	description: string,
}

export type Template = {
	details: TemplateDetails,
	properties: any,
	content: {},
}

export type SelectedItem = {
	id: Id | null,
	template?: Template,
	group?: Group,
	item?: Item
}

export type DefaultFormData = {
	formTemplateId?: Id,
	selectedShift?: any,
	selectedSection?: any,
	selectedDate?: any,
	continue?: boolean,
}

const labelPositions = ['Header','Label','None'];
const headerPositions = ['Header','None'];

const fontSizes = [
	{id:'px', label:'px'},
	{id:'rem', label:'rem'},
	{id:'sm', label:'Small'},
	{id:'base', label:'Medium'},
	{id:'lg', label:'Large'},
	{id:'xl', label:'X Large'},
	{id:'2xl', label:'XX Large'}
];
const inputTypeList = [
	'Checkbox','Color','Date','DropDown','Header',
	'Image','Input','Label','Radio','Signature','Textarea'
];
const inputoptionlist = [
	{id:'email', label:'Email'},
	{id:'number', label:'Number'},
	{id:'password', label:'Password'},
	{id:'phone', label:'Phone'},
	{id:'text', label:'Text'},
];
const dateoptionlist = [
	{id:'date', label:'Date'},
	{id:'datetime-local', label:'Date Time'},
	{id:'time', label:'Time'},
];
const widthunits = [
	{id:'px', label:'px'},
	{id:'%', label:'%'},
	{id:'flex-grow', label:'grow'},
	{id:'w-full', label:'full'},
	{id:'w-fit', label:'fit-content'},
	{id:'w-max', label:'max-content'},
	{id:'w-min', label:'min-content'},
	{id:'w-auto', label:'auto'},
];
const heightunits = [
	{id:'px', label:'px'},
	{id:'%', label:'%'},
	{id:'h-full', label:'full'},
	{id:'h-fit', label:'fit-content'},
	{id:'h-max', label:'max-content'},
	{id:'h-min', label:'min-content'},
	{id:'h-auto', label:'auto'},
];

const justifyitems = [
	{id:'justify-start', label:'start'},
	{id:'justify-end', label:'end'},
	{id:'justify-center', label:'center'},
	{id:'justify-between', label:'between'},
	{id:'justify-around', label:'around'},
	{id:'justify-evenly', label:'evenly'},
	{id:'justify-stretch', label:'stretch'},
	{id:'justify-normal', label:'normal'},
];

const flexitems = [
	{id:'flex-nowrap', label:'no wrap'},
	{id:'flex-wrap', label:'wrap'},
];

const unitslist = ['px','rem','individual'];
const coreunitslist = ['px','rem'];

const dataSourceTypes = ['Custom', 'Sections', 'Shifts'];

const toggleunitvaluefield = {propType: 'number', name: 'unitvaluefield', label: 'Value', defaultValue: 1, dependency: 'parent', condition: {'px':'display','rem':'display'} };
const toggleunittypefield = {propType: 'ddl', name: 'unittypefield', label: 'Units', defaultValue: 'px', options: unitslist, promote: true };

const margingroup = {propType: 'propGroup', name: 'margingroup', label: 'Margin', defaultValue: 'px', propGroup: {unitvaluefield:toggleunitvaluefield, unittypefield:toggleunittypefield}, overflow: ['margintop', 'marginbottom', 'marginleft', 'marginright']};
const unitvaluefield = {propType: 'number', name: 'unitvaluefield', label: 'Value', defaultValue: 1, };
const unittypefield = {propType: 'ddl', name: 'unittypefield', label: 'Units', defaultValue: 'px', options: coreunitslist };
const margintop = { propType: 'propGroup', name: 'margintop', label: 'Margin Top', propGroup: {unitvaluefield, unittypefield}, dependency: 'margingroup', condition: {'individual':'display'}};
const marginbottom = {propType: 'propGroup', name: 'marginbottom', label: 'Margin Bottom', propGroup: {unitvaluefield, unittypefield}, dependency: 'margingroup', condition: {'individual':'display'}};
const marginleft = {propType: 'propGroup', name: 'marginleft', label: 'Margin Left', propGroup: {unitvaluefield, unittypefield}, dependency: 'margingroup', condition: {'individual':'display'}};
const marginright = {propType: 'propGroup', name: 'marginright', label: 'Margin Right', propGroup: {unitvaluefield, unittypefield}, dependency: 'margingroup', condition: {'individual':'display'}};

const paddinggroup = {propType: 'propGroup', name: 'paddinggroup', label: 'Padding', defaultValue: 'px', propGroup: {unitvaluefield:toggleunitvaluefield, unittypefield:toggleunittypefield}, overflow: ['paddingtop', 'paddingbottom', 'paddingleft', 'paddingright']};
const paddingtop = {propType: 'propGroup', name: 'paddingtop', label: 'Padding Top', propGroup: {unitvaluefield, unittypefield}, dependency: 'paddinggroup', condition: {'individual':'display'}};
const paddingbottom = {propType: 'propGroup', name: 'paddingbottom', label: 'Padding Bottom', propGroup: {unitvaluefield, unittypefield}, dependency: 'paddinggroup', condition: {'individual':'display'}};
const paddingleft = {propType: 'propGroup', name: 'paddingleft', label: 'Padding Left', propGroup: {unitvaluefield, unittypefield}, dependency: 'paddinggroup', condition: {'individual':'display'}};
const paddingright = {propType: 'propGroup', name: 'paddingright', label: 'Padding Right', propGroup: {unitvaluefield, unittypefield}, dependency: 'paddinggroup', condition: {'individual':'display'}};

const clearbtn = {propType: 'button', name: 'clearcolorbtn', label: 'Clear', icon: 'ClearIcon', action: 'cleargroup', classes: 'px-10'};
const color = {propType: 'color', name: 'color', label: 'Color', classes: 'px-8'};
const bgcolor = {propType: 'color', name: 'bgcolor', label: 'BG Color', classes: 'px-8'};
const colorgroup = {propType: 'propGroup', name: 'colorgroup', propGroup: {color, clearbtn}};
const bgcolorgroup = {propType: 'propGroup', name: 'bgcolorgroup', propGroup: {bgcolor, clearbtn}};

const requiredfield = {propType: 'cb', name: 'requiredfield', label: 'Required', className: 'pr-1', defaultValue: false};

const key = {propType: 'text', name: 'key', label: 'Key'};
const text = {propType: 'text', name: 'text', label: 'Text'};
const placeholder = {propType: 'text', name: 'placeholder', label: 'Placeholder'};
const header = {propType: 'text', name: 'header', label: 'Header', className: 'pr-1'};
const headerposition = {propType: 'ddl', name: 'headerposition', label: 'Display', options: headerPositions, defaultValue: 'None', className: 'pl-1'};
const headergroup = {propType: 'propGroup', name: 'headergroup', propGroup: {header, headerposition}};
const label = {propType: 'text', name: 'label', label: 'Label', className: 'pr-1'};
const labelposition = {propType: 'ddl', name: 'labelposition', label: 'Display', options: labelPositions, defaultValue: 'Label', className: 'pl-1'};
const labelgroup = {propType: 'propGroup', name: 'labelgroup', propGroup: {label, labelposition}};

const fontsizeunitvaluefield = {propType: 'number', name: 'fontsizeunitvaluefield', label: 'Value', defaultValue: 14, dependency: 'parent', condition: {'px':'display','rem':'display'} };
const fontsizeunittypefield = {propType: 'ddl', name: 'fontsizeunittypefield', label: 'Units', defaultValue: 'base', options: fontSizes, promote: true };
const fontsizegroup = {propType: 'propGroup', name: 'fontsizegroup', label: 'Font Size', defaultValue: 'base', propGroup: {fontsizeunitvaluefield, fontsizeunittypefield} };

const left = {propType: 'button', name: 'left', label: 'Left', icon: 'LeftAlign', classes: 'px-2', promote:true};
const center = {propType: 'button', name: 'center', label: 'Center', icon: 'CenterAlign', classes: 'px-2', promote:true };
const right = {propType: 'button', name: 'right', label: 'Right', icon: 'RightAlign', classes: 'px-2', promote:true };
const positiongroup = {propType: 'buttonGroup', name: 'positiongroup', label: 'Position', defaultValue: 'left', buttonGroup: {left, center, right}};
const type = {propType: 'ddl', name: 'type', label: 'Type', options: inputTypeList, defaultValue: 'Input'};
const inputoptiontype = {propType: 'ddl', name: 'inputoptiontype', label: 'Input Type', options: inputoptionlist, defaultValue: 'text'};
const dateoptiontype = {propType: 'ddl', name: 'dateoptiontype', label: 'Input Type', options: dateoptionlist, defaultValue: 'date'};

const sizeunitvaluefield = {propType: 'number', name: 'sizeunitvaluefield', label: 'Size', defaultValue: 20, className: 'pr-1', dependency: 'parent', condition: {'px':'display','%':'display'} };
const widthunittypefield = {propType: 'ddl', name: 'widthunittypefield', label: 'Size', defaultValue: '%', className: 'pl-1', options: widthunits, promote: true};
const heightunittypefield = {propType: 'ddl', name: 'heightunittypefield', label: 'Size', defaultValue: 'h-fit', className: 'pl-1', options: heightunits, promote: true};

const justifytypefield = {propType: 'ddl', name: 'justifytypefield', label: 'Justify Items', defaultValue: 'justify-normal', className: 'pl-1', options: justifyitems};
const flextypefield = {propType: 'ddl', name: 'flextypefield', label: 'Row Wrap', defaultValue: 'flex-nowrap', className: 'pl-1', options: flexitems};
const rowgroup = {propType: 'propGroup', name: 'rowgroup', propGroup: { justifytypefield, flextypefield }};

const widthgroup = {propType: 'propGroup', name: 'widthgroup', label: 'Width', defaultValue: 'w-fit', propGroup: { sizeunitvaluefield, widthunittypefield }};
const heightgroup = {propType: 'propGroup', name: 'heightgroup', label: 'Height', defaultValue: 'h-fit', propGroup: { sizeunitvaluefield, heightunittypefield}}

const questionheader = {propType: 'text', name: 'questionheader', label: 'Header', className: 'pr-1', condition: {'questionLength': '> 0'}};
const questionheaderdisplay = {propType: 'ddl', name: 'questionheaderdisplay', label: 'Header Display', options: headerPositions, defaultValue: 'None', className: 'pl-1', condition: {'questionLength': '> 0'}};

const questionheadergroup = {propType: 'propGroup', name: 'questionheadergroup', condition: {'questionLength': '> 0'}, propGroup: {questionheader, questionheaderdisplay}};
const questionwidthgroup = {propType: 'propGroup', name: 'questionwidthgroup', label: 'Question Width', defaultValue: '%', condition: {'questionLength': '> 0'}, propGroup: { sizeunitvaluefield, widthunittypefield }};
const questionpositiongroup = {propType: 'buttonGroup', name: 'questionpositiongroup', label: 'Question Position', defaultValue: 'left', condition: {'questionLength': '> 0'}, buttonGroup: {left, center, right}};
const questionfontsizegroup = {propType: 'propGroup', name: 'questionfontsizegroup', label: 'Font Size', defaultValue: 'base', condition: {'questionLength': '> 0'}, propGroup: {fontsizeunitvaluefield, fontsizeunittypefield} };
const grouplabelposition = {propType: 'ddl', name: 'grouplabelposition', label: 'Label Position', options: labelPositions, defaultValue: 'block', condition: {'questionLength': '> 0'}, className: 'pl-1'};

const dropdowndatasourcetype = {propType: 'ddl', name: 'dropdowndatasourcetype', label: 'Data Source', defaultValue: 'Custom', options: dataSourceTypes, data: []};
const filegroup = {propType: 'file', name: 'filegroup'};

export const GroupProperties = {
	questionheader, questionheaderdisplay, questionpositiongroup, questionwidthgroup, questionfontsizegroup,
	rowgroup, margingroup, margintop, marginbottom, marginleft, marginright,
	paddinggroup, paddingtop, paddingbottom, paddingleft, paddingright,
	//colorgroup, bgcolorgroup,
}

export const CheckboxProperties = {
	key, type, headergroup, labelgroup, fontsizegroup, positiongroup, widthgroup, heightgroup,
	margingroup, margintop, marginbottom, marginleft, marginright,
	paddinggroup, paddingtop, paddingbottom, paddingleft, paddingright,
	//colorgroup, bgcolorgroup
}

export const ColorProperties = {
	key, type, headergroup, labelgroup, fontsizegroup, positiongroup, widthgroup, heightgroup,
	margingroup, margintop, marginbottom, marginleft, marginright,
	paddinggroup, paddingtop, paddingbottom, paddingleft, paddingright,
	//colorgroup, bgcolorgroup
}

export const DateProperties = {
	key, type, dateoptiontype, headergroup, labelgroup, fontsizegroup, positiongroup, requiredfield, widthgroup, heightgroup,
	margingroup, margintop, marginbottom, marginleft, marginright,
	paddinggroup, paddingtop, paddingbottom, paddingleft, paddingright,
	//colorgroup, bgcolorgroup
}

export const DDLProperties = {
	key, type, dropdowndatasourcetype, headergroup, labelgroup, fontsizegroup, positiongroup, requiredfield, widthgroup, heightgroup,
	margingroup, margintop, marginbottom, marginleft, marginright,
	paddinggroup, paddingtop, paddingbottom, paddingleft, paddingright,
	//colorgroup, bgcolorgroup
}

export const HeaderProperties = {
	key, type, text, fontsizegroup, positiongroup, widthgroup, heightgroup,
	margingroup, margintop, marginbottom, marginleft, marginright,
	paddinggroup, paddingtop, paddingbottom, paddingleft, paddingright,
	//colorgroup, bgcolorgroup
}

export const ImageProperties = {
	key, type, filegroup, headergroup, labelgroup, fontsizegroup, positiongroup, widthgroup, heightgroup,
	margingroup, margintop, marginbottom, marginleft, marginright,
	paddinggroup, paddingtop, paddingbottom, paddingleft, paddingright,
	//colorgroup, bgcolorgroup
}

export const InputProperties = {
	key, type, inputoptiontype, headergroup, labelgroup, placeholder, fontsizegroup, positiongroup, requiredfield, widthgroup, heightgroup,
	margingroup, margintop, marginbottom, marginleft, marginright,
	paddinggroup, paddingtop, paddingbottom, paddingleft, paddingright,
	//colorgroup, bgcolorgroup
}

export const LabelProperties = {
	key, type, text, fontsizegroup, positiongroup, widthgroup, heightgroup,
	margingroup, margintop, marginbottom, marginleft, marginright,
	paddinggroup, paddingtop, paddingbottom, paddingleft, paddingright,
	//colorgroup, bgcolorgroup
}

export const RadioProperties = {
	key, type, headergroup, labelgroup, fontsizegroup, positiongroup, requiredfield, widthgroup, heightgroup,
	margingroup, margintop, marginbottom, marginleft, marginright,
	paddinggroup, paddingtop, paddingbottom, paddingleft, paddingright,
	//colorgroup, bgcolorgroup
}

export const SignatureProperties = {
	key, type, headergroup, labelgroup, fontsizegroup, positiongroup, requiredfield, widthgroup, heightgroup,
	margingroup, margintop, marginbottom, marginleft, marginright,
	paddinggroup, paddingtop, paddingbottom, paddingleft, paddingright,
	//colorgroup, bgcolorgroup
}

export const TextareaProperties = {
	key, type, headergroup, labelgroup, placeholder, fontsizegroup, positiongroup, requiredfield, widthgroup, heightgroup,
	margingroup, margintop, marginbottom, marginleft, marginright,
	paddinggroup, paddingtop, paddingbottom, paddingleft, paddingright,
	//colorgroup, bgcolorgroup
}