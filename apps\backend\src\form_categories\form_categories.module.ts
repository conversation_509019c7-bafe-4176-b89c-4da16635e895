import { Module } from '@nestjs/common';
import { FormCategoriesController } from './form_categories.controller';
import { FormCategoriesService } from './form_categories.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FormCategory } from './entities/form_category.entity';

@Module({
  controllers: [FormCategoriesController],
  providers: [FormCategoriesService],
  imports: [TypeOrmModule.forFeature([FormCategory])],
})
export class FormCategoriesModule {}
