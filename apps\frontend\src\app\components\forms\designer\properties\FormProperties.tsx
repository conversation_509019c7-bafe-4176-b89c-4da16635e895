import { Group, Id, Item, SelectedItem } from '../../types';
import FormDetails from './FormDetails';
import GroupProperties from './GroupProperties';
import ItemProperties from './ItemProperties';

interface Props {
	currentRevisionId: Id;
	selectedItem: SelectedItem;
	updateGroup: (group: Group) => void;
	updateItem: (item: Item) => void;
	replaceItem: (item: Item, value: any) => void;
	publishActive: boolean;
	setPublishActive: (value: boolean) => void;
	exportFormJSON: () => void;
	importFormJSON: () => void;
	selectHistoricalRecord: (id: Id) => void;
	createRevision: () => void;
}

const FormProperties = ({
	currentRevisionId,
	selectedItem,
	updateGroup,
	updateItem,
	replaceItem,
	publishActive,
	setPublishActive,
	exportFormJSON,
	importFormJSON,
	selectHistoricalRecord,
	createRevision
}: Props) => {
	const { template, group, item } = selectedItem;
	
	return (
		<div className="">
			{template &&
				<FormDetails
					currentRevisionId={currentRevisionId}
					template={template}
					publishActive={publishActive}
					setPublishActive={setPublishActive}
					exportFormJSON={exportFormJSON}
					importFormJSON={importFormJSON}
					selectHistoricalRecord={selectHistoricalRecord}
					createRevision={createRevision}
				/>
			}
			{group &&
				<GroupProperties group={group} updateGroup={updateGroup} />
			}
			{item &&
				<ItemProperties item={item} updateItem={updateItem} replaceItem={replaceItem} />
			}
		</div>
	);
};

export default FormProperties;