import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Shift } from './entities/shift.entity';
import { Not, Repository } from 'typeorm';
import { CreateShiftDto } from './dto/create-shift.dto';
import { UpdateShiftDto } from './dto/update-shift.dto';

@Injectable()
export class ShiftsService {
  constructor(
    @InjectRepository(Shift)
    private shiftRepository: Repository<Shift>
  ) {}

  async create(createShiftDto: CreateShiftDto, mineId: number) {
    createShiftDto.mineId = mineId;
    createShiftDto.isActive = true;
    createShiftDto.extShiftId = -1;
    return await this.shiftRepository.save(createShiftDto);
  }

  async findAll(mineId: number) {
    return this.shiftRepository.find({
      where: {
        mineId: mineId,
        isActive: true,
        shift_type: Not('Maintainance'),
      },
    });
  }

  async findOne(id: number) {
    return this.shiftRepository.findOneBy({ id });
  }

  async findByName(name: string) {
    const shiftData = await this.shiftRepository.findOneBy({
      shiftName: name,
    });

    return shiftData;
  }

  async findAllShifts() {
    return this.shiftRepository.find();
  }

  async findActiveShifts(mineId: number) {
    return this.shiftRepository.find({
      where: {
        mineId: mineId,
        isActive: true,
      },
    });
  }

  async update(id: number, updateShiftDto: UpdateShiftDto) {
    const shiftToUpdate = await this.shiftRepository.findOneBy({ id });

    if (!shiftToUpdate) {
      throw new NotFoundException('Role not Found!');
    }

    const updatedShift = Object.assign(shiftToUpdate, updateShiftDto);
    return await this.shiftRepository.save(updatedShift);
  }

  async updateShiftStatus(id: number) {
    let shift = await this.findOne(id);
    if (!shift) {
      throw new NotFoundException(`Shift with given id ${id} is not found`);
    } else {
      if (shift.isActive === true) {
        shift.isActive = false;
      } else {
        shift.isActive = true;
      }
      return this.shiftRepository.save(shift);
    }
  }
}
