import { useDraggable } from '@dnd-kit/core';
import { Tool } from '../../types';
import { ReactNode } from 'react';

interface Props {
	tool: Tool;
  children: ReactNode | ReactNode[];
}

const Draggable = ({tool, children}: Props) => {
  const toolItem = {
    id: tool.id,
    label: tool.label,
  };
  const {attributes, listeners, setNodeRef} = useDraggable({
    id: tool.id,
    data: {
      type: 'ToolItem',
      toolItem,
      children: children
    }
  });
  
  return (
    <div ref={setNodeRef} {...listeners} {...attributes} className="hover:ring">
      {children}
    </div>
  );
}

export default Draggable;