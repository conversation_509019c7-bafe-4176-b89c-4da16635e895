import { Injectable, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Role } from '../roles/entities/role.entity';
import { UserRole } from './entities/user-role.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserMine } from './entities/user-mine.entity';
import { EmailService } from '../common/email/email.service';
import { ConfigService } from '@nestjs/config';
import { MinesService } from '../mines/mines.service';
import { ResetPassword } from './dto/reset-password';
import * as bcrypt from 'bcrypt';
import { ForbiddenException } from '@nestjs/common';
import { Company } from '../companies/entities/company.entity';
import { Mine } from '../mines/entities/mine.entity';
import { AuditService } from '../audit/audit.service';
import { RolesService } from '../roles/roles.service';

@Injectable()
export class UsersService {
  constructor(
    private emailService: EmailService,
    private readonly configService: ConfigService,
    private mineService: MinesService,
    private auditService: AuditService,
    private roleService: RolesService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserRole)
    private userRoleRepository: Repository<UserRole>,
    @InjectRepository(UserMine)
    private userMineRepository: Repository<UserMine>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>
  ) {}

  generatePassword(username: string, length: number = 8): string {
    const charset =
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789~!@#$%^&*_-+=.?/<>';
    let password = '';

    while (true) {
      password = '';
      for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * charset.length);
        password += charset[randomIndex];
      }

      if (
        password.length >= 8 &&
        !password.includes(username) &&
        /[a-z]/.test(password) &&
        /[A-Z]/.test(password) &&
        /\d/.test(password) &&
        /[^a-zA-Z0-9]/.test(password)
      ) {
        return password;
      }
    }
  }

  async findByUsername(username: string) {
    const userData1 = await this.userRepository.findOneBy({
      username: username,
    });

    return userData1;
  }

  async findMineIsDeleted(username: string) {
    const userData = await this.userRepository
      .createQueryBuilder('users')
      .select(['mine.is_delete as isMineDeleted ,mine.is_active  as isActive'])
      .leftJoin('users_mines', 'userMine', 'users.id = userMine.user_id')
      .leftJoin('mines', 'mine', 'mine.id = userMine.mine_id')
      .where('users.username = :username', { username: username })
      .getRawMany();

    if (userData[0].isActive == null) {
      userData[0].isActive = true;
    }

    return userData[0];
  }

  async findByEmail(email: string) {
    const userData = await this.userRepository.findOneBy({
      email: email,
    });
    return userData;
  }

  async getUsersWithRole(userId: number) {
    const queryBuilder = this.userRoleRepository
      .createQueryBuilder('users_roles')
      .select([
        'role.name AS RoleName',
        'feature.name AS FeatureName',
        'role.id AS RoleId',
        'users_roles.id AS UsersRolesId',
        'feature.id as FeatureId',
        // 'feature.sort_order as FeatureOrder',
      ])
      .innerJoin('roles', 'role', 'role.id = users_roles.roleId')
      .innerJoin(
        'role_feature_access',
        'roleFeature',
        'roleFeature.roleId = role.id'
      )
      .innerJoin('features', 'feature', 'feature.id = roleFeature.featureId')
      .where('users_roles.user_id = :userId', { userId: userId })
    .addOrderBy('feature.sort_order', 'ASC');
    const userData = await queryBuilder.getRawMany();

    return userData;
  }

  async getUserRole(userId: number) {
    const queryBuilder = this.userRoleRepository
      .createQueryBuilder('users_roles')
      .select([
        'role.name AS RoleName',
        'role.id AS RoleId',
        'users_roles.id AS UsersRolesId',
      ])
      .innerJoin('roles', 'role', 'role.id = users_roles.roleId')
      .where('users_roles.user_id = :userId', { userId: userId });
    const userData = await queryBuilder.getRawMany();
    return userData;
  }

  async findAll(companyId: number) {
    const userData = await this.userRepository
      .createQueryBuilder('users')
      .select([
        'users.id AS userId',
        'users.firstName',
        'users.lastName',
        'users.email',
        'users.username',
        'users.isActive',
        'role.id AS roleId',
        'role.name AS roleName',
      ])
      .innerJoin('users_roles', 'userRole', 'users.id = userRole.user_id')
      .innerJoin('roles', 'role', 'role.id = userRole.role_id')
      .where('users.company_id = :companyId', { companyId: companyId })
      .andWhere('users.is_delete = :isDelete', { isDelete: false })
      .addOrderBy('users.created_at', 'DESC')
      .getRawMany();
    return userData;
  }

  async findUserById(id: number): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: id } });
    if (!user) throw new NotFoundException(`User with id ${id} not found`);
    return user;
  }

  async createUser(createUserDto: CreateUserDto, user?: any): Promise<User> {
    let newUser: User;
    let newUserRole: UserRole;

    try {
      let newUserMine: UserMine;
      let password = this.generatePassword(createUserDto.username);
      const emailPassword = password
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');

      let mine: any;

      if (!createUserDto.mineId) {
        mine = await this.mineService.getMineByCompanyId(
          user.mineCompanyId ? user.mineCompanyId : user.companyid
        );
      }

      let companyName = await this.companyRepository.findOne({
        where: { id: user.companyid },
      });

      createUserDto = {
        companyId: !createUserDto.companyId
          ? user.mineCompanyId
            ? user.mineCompanyId
            : user.companyid
          : createUserDto.companyId,
        firstName: createUserDto.firstName,
        lastName: createUserDto.lastName,
        roleId: createUserDto.roleId,
        mineId: createUserDto.mineId ? createUserDto.mineId : mine.id, //for compay admin creation on company onboard processs we are sending the mmine id explicitly.
        username: createUserDto.username,
        email: createUserDto.email,
        password: password,
      };
      newUser = this.userRepository.create(createUserDto);

      const mail = {
        to: createUserDto.email,
        subject: 'Login credentials for IWT platform',
        from: this.configService.get<string>('SEND_GRID_FROM_EMAIL'),
        html: `<p>Dear ${createUserDto.firstName} ${createUserDto.lastName},</p>
          <p>Welcome to IWT Platform! Your account has been successfully created. Below are your login credentials:</p>
          <p>Username: ${createUserDto.username}</p>
          <p>Password: ${emailPassword}</p>
          <p>Please use these credentials to log in to your account.</p>
          <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
         <p>Best regards,<br> ${companyName.name} Support Team</p>`,
      };

      const savedUser = await this.userRepository.save(newUser);
      await this.emailService.sendEmail(mail);
      if (savedUser.id) {
        const UserRole = {
          userId: savedUser.id,
          roleId: createUserDto.roleId, //will get role_id from UI
          createdBy: user.userId,
        };
        newUserRole = this.userRoleRepository.create(UserRole);
        newUserRole = await this.userRoleRepository.save(newUserRole); //to save data in users-roles table
        const userMine = {
          userId: savedUser.id,
          mineId: !createUserDto.mineId ? mine.id : createUserDto.mineId,
          createdBy: user.userId,
        };
        newUserMine = this.userMineRepository.create(userMine);
        newUserMine = await this.userMineRepository.save(newUserMine); // to save data in users-mineschang table
        const roleData = await this.roleService.findOne(newUserRole.roleId);
        this.auidtLog(
          user,
          'User added',
          savedUser.firstName + ' ' + savedUser.lastName,
          roleData.name
        );
      }
      return newUser;
    } catch (error) {
      if (newUser) {
        await this.userRepository.delete(newUser.id);
        throw error;
      }
      if (UserRole) {
        await this.userRoleRepository.delete(newUserRole.id);
      }
      console.error('Error creating user and associated entities:', error);
    }
  }

  async updateUser(
    userId: number,
    UpdateUserDto: UpdateUserDto,
    reqUser?: any
  ) {
    if (!UpdateUserDto.isActive) {
      const user = await this.findUserById(userId);
      if (!user) {
        throw new NotFoundException(`User with id ${userId} not found`);
      }

      if (UpdateUserDto.roleId) {
        const userRole = await this.getUserRole(user.id);

        const existingRoleId = userRole[0].RoleId;

        if (existingRoleId != Number(UpdateUserDto.roleId)) {
          if (userId == Number(reqUser?.userId)) {
            throw new ForbiddenException();
          } else {
            const id = userRole[0].UsersRolesId;
            this.userRoleRepository.delete(id);
            let newUserRole: UserRole;
            const UserRole = {
              userId: userId,
              roleId: Number(UpdateUserDto.roleId),
            };

            newUserRole = await this.userRoleRepository.create(UserRole);
            newUserRole = await this.userRoleRepository.save(newUserRole);
          }
        }

        const { roleId, mineId, ...restOfUser } = UpdateUserDto;

        this.auidtLog(
          reqUser,
          'User updated',
          user.firstName + ' ' + user.lastName,
          userRole[0].RoleName
        );
        return this.userRepository.update(userId, restOfUser);
      }

      if (UpdateUserDto.password) {
        const hashedPassword = await bcrypt.hash(UpdateUserDto.password, 10);
        UpdateUserDto.password = hashedPassword;
      }

      // return this.userRepository.update(id, UpdateUserDto);
      Object.assign(user, UpdateUserDto);
      return await this.userRepository.save(user);
    }
  }

  async updateUserStatus(id: number, reqUser: any) {
    const user = await this.findUserById(id);
    if (!user) {
      throw new NotFoundException(`User with id ${id} not found`);
    } else {
      const userRole = await this.getUserRole(id);
      if (user.isActive === true) {
        user.isActive = false;
        this.auidtLog(
          reqUser,
          'User deactivated',
          user.firstName + ' ' + user.lastName,
          userRole[0].RoleName
        );
      } else {
        user.isActive = true;
        this.auidtLog(
          reqUser,
          'User activated',
          user.firstName + ' ' + user.lastName,
          userRole[0].RoleName
        );
      }
      return this.userRepository.save(user);
    }
  }

  async generateUniqueUsername(
    firstName: string,
    lastName: string
  ): Promise<string> {
    let basename = firstName.charAt(0).toLowerCase() + lastName.toLowerCase();
    let userName = basename;
    let counter = 1;

    while (await this.findByUsername(userName)) {
      userName = basename + counter;
      counter++;
    }
    return userName;
  }

  async deleteUser(id: number, reqUser: any) {
    let user = await this.findUserById(id);
    if (!user) {
      throw new NotFoundException(`user with given id ${id} is not found`);
    } else {
      if (user.isDelete === true) {
        user.isDelete = false;
      } else {
        user.isDelete = true;
      }
      const userRole = await this.getUserRole(id);
      this.auidtLog(
        reqUser,
        'User deleted',
        user.firstName + ' ' + user.lastName,
        userRole[0].RoleName
      );
      return this.userRepository.save(user);
    }
  }

  async resetPassword(resPassword: ResetPassword) {
    const user = await this.userRepository.findOne({
      where: { email: resPassword.email },
    });

    if (user) {
      let companyName = await this.companyRepository.findOne({
        where: {
          id: user.companyId,
        },
      });
      if (user.isDelete) {
        throw new ForbiddenException();
      }

      if (!user.isActive) {
        throw new ForbiddenException();
      }
      const newPassword = await this.generatePassword(user.username);
      const emailPassword = newPassword
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');
      const mail = {
        to: resPassword.email,
        subject: 'Password Reset Request',
        from: this.configService.get<string>('SEND_GRID_FROM_EMAIL'),
        html: `<p>Dear ${user.firstName} ${user.lastName},</p>
          <p>You recently requested to reset your password for your account. Please use this credentials to log in to your account.</p>
          <p>Username : ${user.username}</p>
          <p>Password : ${emailPassword}</p>
          <p>Thank you,</p>
          <p>Best regards,<br> ${companyName.name} Support Team</p>`,
      };

      await this.emailService.sendEmail(mail);

      const updateUserDto: UpdateUserDto = {
        password: newPassword,
        updatedBy: user.id,
      };
      await this.updateUser(user.id, updateUserDto);
    } else {
      throw new NotFoundException(
        `user with given email ${resPassword} is not found`
      );
    }
  }

  async auidtLog(
    user: any,
    action: string,
    forUser: string,
    forUserRole: string
  ) {
    const mine = await this.mineService.findMineById(user.mineid);
    this.auditService.createAuditLog(
      user,
      mine.name,
      action,
      forUser,
      forUserRole
    );
  }
}
