import { useEffect, useState } from 'react';
import { Link, Outlet, useNavigate, useParams } from 'react-router-dom';
import AutocompleteWithButton from './AutocompleteWithAction';
import { useAddUserWatchlist } from '../../../services/mutations/watchlistmutations';
import { UserMenu } from './UserMenu';
import {
  WatchlistData,
  getMineAllMiners,
} from '../../../api/users/watchlistapis';
import decodeJWT from '../../../utils/jwtdecoder';
import SortingDropdown from './SortingDropdown';
import IWTDatePicker from './IWTDatePicker';
import { ActiveTabIcon } from '../../../assets/icons/icons';
import { ProdActiveTabIcon } from '../../../assets/icons/icons';
import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { getPersonalReportData } from '../../../api/location/locationdashboardapi';
import SingleSelectedDatePicker from './SingleSelectDatePicker';
import { toast } from 'react-toastify';
import { getPageNamesFromUrl } from '../PageName';

interface SubMenuInnerNavBarProps {
  leftTitle: string;
  rightTitle: string;
  rightSubTitle1?: string;
  showSearchBox: boolean;
  tabNames: string[];
  pathToRender: string;
  defaultTab?: string;
  sectionData?: any;
  watchlistData?: any;
  setArrive?: any;
  setSelectedDate?: any;
  date?: any;
  setDate?: any;
  selectedDate?: any;
  arrive?: any;
  repSecDate?: any;
  setRepSecDate?: any;
  rightSubTitle2?: any;
  prodRepstartDate?: any;
  setProdRependDate?: any;
  firstDatePicker: boolean;
  secondDatePicker: boolean;
  reportMineDate: any;
  setReportMineDate: any;
  compareToDate: any;
  setCompareToDate: any;
  compareFromDate: any;
  setCompareFromDate: any;
  abbriviation: string;
  personnelRange: any;
  setPersonnelRange: any;
  setCategoryFilter: any;
}

const SubMenuInnerNavBar = ({
  leftTitle,
  rightTitle,
  rightSubTitle1,
  tabNames,
  showSearchBox,
  pathToRender,
  defaultTab,
  watchlistData,
  setArrive,
  setSelectedDate,
  date,
  setDate,
  selectedDate,
  arrive,
  repSecDate,
  setRepSecDate,
  rightSubTitle2,
  firstDatePicker,
  secondDatePicker,
  reportMineDate,
  setReportMineDate,
  compareToDate,
  setCompareToDate,
  compareFromDate,
  setCompareFromDate,
  personnelRange,
  setPersonnelRange,
  setCategoryFilter
}: SubMenuInnerNavBarProps) => {
  const [searchText, setSerachText] = useState('');
  const [lastUpdatedDate, setLastUpdatedDate] = useState();
  const [lastEformSubmittedd, setlastEformSubmitted] = useState();
  const [timezone, setTimezone] = useState();
  const [selectedMenu, setSelectedMenu] = useState(defaultTab);
  const [leftTitleHeader, setLeftTitleHeader] = useState(leftTitle);
  const [firstDate, setFirstDate] = useState({
    startDate: null,
    endDate: null,
  });
  const [secondDate, setSecondDate] = useState({
    startDate: null,
    endDate: null,
  });
  const [isValidDateRange, setValidDateRange] = useState(true);
  const addUserWatchlistItem = useAddUserWatchlist();
  const navigate = useNavigate();
  const params = useParams();
  const requestUrl = params['*'];
  const pathToRenderLength = pathToRender?.length;
  let menu = requestUrl?.substring(pathToRenderLength + 1, requestUrl?.length);
  const decoded = decodeJWT();

  const locationLive = requestUrl?.includes('Location/live');
  const locationReport = requestUrl?.includes('Location/report');
  const productionLive = requestUrl?.includes('Production/live/');
  const productionReport = requestUrl?.includes('Production/report/');
  const newForms = requestUrl?.includes('Forms/New');
  const completedForms = requestUrl?.includes('Forms/Completed');

  useEffect(() => {
    if (
      pathToRender == 'Production/live' ||
      pathToRender == 'Production/report'
    ) {
      navigate(`/app/${pathToRender}/${selectedMenu ? selectedMenu : 'mine'}`);
    } else if(newForms || completedForms) {
      setCategoryFilter(selectedMenu);
    } else {
      navigate(
        `/app/${pathToRender}/${selectedMenu == undefined ? '' : selectedMenu}`
      );
    }
  }, [selectedMenu]);
  if (selectedMenu != menu && !(newForms || completedForms)) {
    setSelectedMenu(menu);
  }

  let data;
  if (locationLive) {
    data =
      selectedMenu == 'watchlist'
        ? ''
        : selectedMenu == 'sections'
        ? arrive
        : '';
  }

  if (locationReport) {
    data =
      selectedMenu == 'personnel' ||
      selectedMenu == `personnel/${params?.minerId}`
        ? personnelRange
        : selectedMenu == 'sections' ||
          selectedMenu ==
            `sections/${params.selectedDate}/${params?.sectionName}`
        ? selectedDate
        : selectedMenu == 'checkins'
        ? date
        : '';
  }

  let filterDate = {
    compareFromDate: compareFromDate,
    compareToDate: compareToDate,
  };

  if (productionReport) {
    data =
      selectedMenu == 'sections' ||
      selectedMenu == `sections/${params?.sectionId}`
        ? repSecDate
        : selectedMenu == 'mine'
        ? reportMineDate
        : selectedMenu == 'compare' && filterDate;
  }
  const mapObject = (source: any): WatchlistData => {
    return {
      minerId: source.MinerId,
      userId: decodeJWT()?.userId,
    };
  };
  const handelWatchActionClick = async (data: any) => {
    const watchlistItem = mapObject(data);
    const res = await addUserWatchlistItem.mutateAsync(watchlistItem);
  };

  const handelOptionClick = async (data: any) => {
    if (
      (data.minerName && selectedMenu === 'personnel') ||
      (data.minerName && selectedMenu === `personnel/${params?.minerId}`)
    )
      navigate(
        params?.mineId
          ? `/app/Mines/${params?.mineId}/Location/report/personnel/${data?.MinerId}`
          : `/app/Location/report/personnel/${data?.MinerId}`
      );
    // setLeftTitleHeader(data?.minerName);
  };

  const { data: minerData, isLoading } = useQuery({
    queryKey: ['miners'],
    queryFn: getMineAllMiners,
    refetchOnWindowFocus: false,
  });

  // Parse input datetime string
  const datetime = dayjs(lastUpdatedDate);

  // Format the date
  const formattedDate = ` Today ` + datetime.format('[@] hh:mm a');

  const prodFormattedDate = ` Today ` + datetime.format('[@] hh:mm a');

  const EformSubmitted = lastEformSubmittedd;

  const formattedEformDate = dayjs(EformSubmitted).format(
    ' MM-DD-YY [@] hh:mm a'
  );

  useEffect(() => {
    if (localStorage.getItem('refreshingPage')) {
      localStorage.removeItem('refreshingPage');
      setTimeout(() => {
        toast.info(
          `Your Role has been updated to ${
            decodeJWT()?.role.charAt(0).toUpperCase() +
            decodeJWT()?.role.slice(1)
          }`
        );
      }, 1000);
    }
  });

  return (
    <div className="m-auto">
      <div className="m-auto sticky top-0 z-30 agBreakup2">
        <div className="px-10 xl:px-10 2xl:px-16 pt-2 flex flex-column justify-between align-center">
          <h6 className="py-2  font-bold text-white text-[32px]">
            {params?.minerId
              ? minerData?.data?.find(
                  (ele: any) => ele?.MinerId == Number(params?.minerId)
                )?.minerName ?? ''
              : leftTitleHeader}
          </h6>
          {showSearchBox && (
            <AutocompleteWithButton
              setSerachText={setSerachText}
              isAutoComplete={
                selectedMenu == 'watchlist' ||
                selectedMenu == 'personnel' ||
                selectedMenu == `personnel/${params?.minerId}`
                  ? true
                  : false
              }
              isWithButton={selectedMenu == 'watchlist' ? true : false}
              miners={minerData?.data}
              watchAction={handelWatchActionClick}
              optionClick={handelOptionClick}
            />
          )}

          <div className="grid gap-0">
            <div
              className={`${
                locationLive || productionLive ? 'py-2' : ''
              } flex justify-end items-center -mr-15 relative`}
            >
              <h6 className="font-bold text-white text-[32px]">{rightTitle}</h6>
              <span className="ml-4">
                <UserMenu />
              </span>
            </div>
            {/* <h6 className="font-bold text-white text-[32px] text-right flex-col mt-[9px]">
              {rightTitle}
            </h6> */}
            {locationLive && (
              <div className="text-right mr-[-7px]">
                <span className="text-[#ffb132] text-[12px]">
                  {rightSubTitle1}:
                </span>
                <span className="text-white text-[12px] mr-2">
                  {`${locationLive ? formattedDate : prodFormattedDate}`}
                </span>
              </div>
            )}
            {productionLive && rightSubTitle2 && (
              <>
                <div className=" text-end mr-[-7px]">
                  <span className="text-[#ffb132] text-[12px]">
                    {rightSubTitle1}:
                  </span>
                  <span className="text-white text-[12px] mr-2">
                    {`${locationLive ? formattedDate : prodFormattedDate}`}
                  </span>
                </div>

                <div className="mr-[-7px]">
                  <span className="text-[#ffb132] text-[12px]">
                    {rightSubTitle2}:
                  </span>
                  <span className="text-white text-[12px] mr-2">
                    {lastEformSubmittedd == null ? (
                      <span className="text-[16px] font-[900] ml-[10px]">
                        -
                      </span>
                    ) : (
                      `${locationLive ? formattedDate : formattedEformDate} ${
                        timezone ? timezone : ''
                      }`
                    )}
                  </span>
                </div>
              </>
            )}
          </div>
        </div>
        <div
          className={`pl-10 xl:mx-10 2xl:mx-16 flex border-b-[1px]  border-[#80c2fe]  ${
            locationLive && selectedMenu !== 'sections' ? 'pb-1.5' : ''
          }
          `}
        >
          <div
            className={`flex flex-column justify-start  text-white cursor-pointer text-[14px] font-normal
            ${newForms || completedForms ? 'w-[auto]' : 'w-[35%]'}
            ${productionReport ? 'pt-3' : ''}
            ${locationLive ? 'mt-2' : `${locationReport ? 'mt-3' : ''}`}`}
          >
            {tabNames.map((tab, index) => (
              <Link
                to={
                  tab == 'personnel'
                    ? params?.minerId
                      ? `/app/${pathToRender}/${tab}/${params?.minerId}`
                      : locationLive &&
                        params['*']?.includes('Location/live/sections') &&
                        params?.sectionName
                      ? `/app/${pathToRender}/${tab}/${params?.sectionId}`
                      : productionReport &&
                        params['*']?.includes('Production/report/sections') &&
                        params?.sectionId
                      ? `/app/${pathToRender}/${tab}/${params?.sectionId}`
                      : `/app/${pathToRender}/${tab}`
                    : newForms || completedForms
                      ? `/app/${pathToRender}?${tab}`
                      : `/app/${pathToRender}/${tab}`
                }
                onClick={(e) => {
                  setSelectedMenu(tab);
                  if (tab !== 'personnel') {
                    setLeftTitleHeader(leftTitle);
                  }
                  setSerachText('');
                }}
                key={tab}
                className={`${index === 0 ? 'mr-7' : 'mx-8'}
                ${selectedMenu === tab ? 'text-[#FFB132]' : ''}
                ${tab == 'mine' ? 'w-[46px]' : newForms || completedForms ? 'w-auto min-w-[66px]' : 'w-[66px]'}
                  
                `}
              >
                <span
                  className={`cursor-pointer ${
                    selectedMenu === tab
                      ? 'border-b-[5px] pb-[7px] border-[#FFB132]'
                      : ''
                  } ${
                    tab == 'personnel' && params?.minerId
                      ? 'border-b-[5px] pb-[7px] border-[#FFB132]'
                      : ''
                  }`}
                >
                  {(tab == 'personnel' && params?.minerId) ||
                  (tab == 'sections' &&
                    ((params?.sectionName && params?.selectedDate) ||
                      params?.sectionId)) ? (
                    <span className="text-[#FFB132]">{tab.toUpperCase()}</span>
                  ) : (
                    tab.toUpperCase()
                  )}
                </span>
                {
                  selectedMenu === tab ? (
                    <div
                      className={` ${
                        locationLive && selectedMenu == 'sections'
                          ? 'h-0'
                          : 'h-1'
                      } ${
                        selectedMenu === 'watchlist' ? 'w-[76px] ' : 'w-[66px]'
                      } ${productionLive || productionReport ? 'h-2.5' : ''}
                    `}
                    >
                      {locationLive || locationReport ? '' : ''}
                    </div>
                  ) : (
                    ''
                  )
                  //  (
                  //   <>
                  //     {(tab == 'personnel' && params?.minerId) ||
                  //     (tab == 'sections' &&
                  //       ((params?.sectionName && params?.selectedDate) ||
                  //         params?.sectionId)) ? (
                  //       <>
                  //         <span className="border-b-[5px] pb-[7px] border-[#FFB132]"></span>
                  //       </>
                  //     ) : (
                  //       ''
                  //     )}
                  //   </>
                  // )
                }{' '}
              </Link>
            ))}
          </div>

          <div
            className={`flex justify-end  pb-1.5 ${
              selectedMenu === 'sections' && locationLive ? 'w-full pl-80' : ''
            }`}
          >
            <div className="flex justify-end w-full pl-10">
              {locationLive && selectedMenu == 'sections' ? (
                <div className="">
                  <div className="flex justify-end w-full ">
                    {locationLive && <SortingDropdown setArrive={setArrive} />}
                  </div>
                </div>
              ) : (
                ''
              )}
            </div>
          </div>

          {((selectedMenu === 'sections' ||
            selectedMenu ==
              `sections/${params.selectedDate}/${params?.sectionName}` ||
            selectedMenu === 'checkins') &&
            locationReport) ||
          (productionReport &&
            (selectedMenu === 'sections' ||
              selectedMenu === `sections/${params?.sectionId}`)) ? (
            <div className="flex justify-end w-[100%] pb-0.5">
              <div className="">
                <SingleSelectedDatePicker
                  asSingle={true}
                  selectedDate={selectedDate}
                  setSelectedDate={setSelectedDate}
                  date={date}
                  setDate={setDate}
                  showShortcuts={false}
                  repSecDate={repSecDate}
                  setRepSecDate={setRepSecDate}
                />
              </div>
            </div>
          ) : selectedMenu === 'compare' ? (
            <div className="w-full">
              <div className="flex justify-end w-[100%]  pb-0.5">
                <div className="w-[270px] ">
                  <IWTDatePicker
                    asSingle={false}
                    showShortcuts={false}
                    firstDatePicker={true}
                    setCompareFromDate={setCompareFromDate}
                    compareFromDate={compareFromDate}
                    setFirstDate={setFirstDate}
                    firstDate={firstDate}
                    secondDate={secondDate}
                    isValidDateRange={isValidDateRange}
                  />
                </div>
                <div className="w-[272px] ml-2">
                  <IWTDatePicker
                    asSingle={false}
                    showShortcuts={false}
                    secondDatePicker={true}
                    compareToDate={compareToDate}
                    setCompareToDate={setCompareToDate}
                    setSecondDate={setSecondDate}
                    firstDate={firstDate}
                    secondDate={secondDate}
                    isValidDateRange={isValidDateRange}
                  />
                </div>
              </div>
            </div>
          ) : productionReport && selectedMenu === 'mine' ? (
            <div className="flex justify-end w-[100%] pb-0.5">
              <div className="w-[272px]">
                <IWTDatePicker
                  asSingle={false}
                  showShortcuts={true}
                  reportMineDate={reportMineDate}
                  setReportMineDate={setReportMineDate}
                />
              </div>
            </div>
          ) : (
            (selectedMenu === 'personnel' || params?.minerId) && (
              <div className="flex justify-end w-[100%] pb-0.5">
                <div className="w-[272px]">
                  <IWTDatePicker
                    asSingle={false}
                    personnelRange={personnelRange}
                    setPersonnelRange={setPersonnelRange}
                    showShortcuts={true}
                  />
                </div>
              </div>
            )
          )}
        </div>
      </div>

      <div className="sticky ">
        <Outlet
          context={[
            searchText,
            setLastUpdatedDate,
            data,
            setlastEformSubmitted,
            setTimezone,
          ]}
        />
      </div>
    </div>
  );
};

export default SubMenuInnerNavBar;
