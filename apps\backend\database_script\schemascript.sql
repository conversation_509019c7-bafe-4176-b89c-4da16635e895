
/****** Object:  Table [dbo].[categories]    Script Date: 20-02-2024 16:39:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[categories](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[description] [nvarchar](255) NOT NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_24dbc6126a28ff948da33e97d3b] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [UQ_8b0be371d28245da6e4f4b61878] UNIQUE NONCLUSTERED 
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[companies]    Script Date: 20-02-2024 16:39:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[companies](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[code] [nvarchar](255) NOT NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_d4bc3e82a314fa9e29f652c2c22] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [UQ_3dacbb3eb4f095e29372ff8e131] UNIQUE NONCLUSTERED 
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [UQ_80af3e6808151c3210b4d5a2185] UNIQUE NONCLUSTERED 
(
	[code] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[features]    Script Date: 20-02-2024 16:39:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[features](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[code] [nvarchar](255) NOT NULL,
	[category_id] [int] NOT NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_5c1e336df2f4a7051e5bf08a941] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [UQ_bcc3a344ae156a9fba128e1cb4d] UNIQUE NONCLUSTERED 
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[mines]    Script Date: 20-02-2024 16:39:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[mines](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[location] [nvarchar](255) NOT NULL,
	[code] [nvarchar](255) NOT NULL,
	[is_active] [bit] NOT NULL,
	[company_id] [int] NOT NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_92ece89c5f00ec1f5b3c697bd1f] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [UQ_886c4b08c28ae36315ae9186378] UNIQUE NONCLUSTERED 
(
	[code] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[role_feature_access]    Script Date: 20-02-2024 16:39:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[role_feature_access](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[role_id] [int] NOT NULL,
	[feature_id] [int] NOT NULL,
	[has_read_access] [bit] NOT NULL,
	[has_write_access] [bit] NOT NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_b69baffb7faac9df498732291a1] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[roles]    Script Date: 20-02-2024 16:39:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[roles](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[company_id] [int] NOT NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_c1433d71a4838793a49dcad46ab] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[users]    Script Date: 20-02-2024 16:39:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[users](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[username] [nvarchar](255) NOT NULL,
	[company_id] [int] NOT NULL,
	[password] [nvarchar](255) NOT NULL,
	[first_name] [nvarchar](255) NOT NULL,
	[last_name] [nvarchar](255) NOT NULL,
	[phone] [nvarchar](255) NULL,
	[job_title] [nvarchar](255) NULL,
	[email] [nvarchar](255) NOT NULL,
	[is_active] [bit] NOT NULL,
	[is_delete] [bit] NOT NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_a3ffb1c0c8416b9fc6f907b7433] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UQ_97672ac88f789774dd47f7c8be3] UNIQUE NONCLUSTERED 
(
	[email] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UQ_fe0bb3f6520ee0469504521e710] UNIQUE NONCLUSTERED 
(
	[username] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[users_mines]    Script Date: 20-02-2024 16:39:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[users_mines](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[user_id] [int] NOT NULL,
	[mine_id] [int] NOT NULL,
	[is_active] [bit] NOT NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_950e24a27c1a645b9498f87eab6] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[users_roles]    Script Date: 20-02-2024 16:39:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[users_roles](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[user_id] [int] NOT NULL,
	[role_id] [int] NOT NULL,
	[is_active] [bit] NOT NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_1d8dd7ffa37c3ab0c4eefb0b221] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [UQ_e4435209df12bc1f001e5360174] UNIQUE NONCLUSTERED 
(
	[user_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE miners (
  id bigint IDENTITY(1,1),
  mine_id int NOT NULL, 

  ext_miner_id varchar(128) NOT NULL UNIQUE,
  first_name varchar(128) NOT NULL,
  last_name varchar(128) NOT NULL,
  ext_node_id int,
  occupation_id int,
  created_at datetime2 NOT NULL,
  updated_at datetime2 NOT NULL,
  is_active bit NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_miners_mine_id FOREIGN KEY (mine_id) REFERENCES mines(id)  
);


/****** Object:  Table [dbo].[watchlist_items]    Script Date: 20-02-2024 16:39:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[watchlist_items](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[user_id] [int] NOT NULL,
	[miner_id] [bigint] NOT NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_0a02323c5cc02e094871f24062b] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[categories] ADD  CONSTRAINT [DF_a7b2c155b5bad01eb952cf2e562]  DEFAULT (GETUTCDATE()) FOR [created_at]
GO
ALTER TABLE [dbo].[categories] ADD  CONSTRAINT [DF_55daad89e067f87627f9f9d8586]  DEFAULT (GETUTCDATE()) FOR [updated_at]
GO
ALTER TABLE [dbo].[companies] ADD  CONSTRAINT [DF_b559ae26b6f801536d281094530]  DEFAULT (GETUTCDATE()) FOR [created_at]
GO
ALTER TABLE [dbo].[companies] ADD  CONSTRAINT [DF_309c37c80e3d34b32ca2381880f]  DEFAULT (GETUTCDATE()) FOR [updated_at]
GO
ALTER TABLE [dbo].[features] ADD  CONSTRAINT [DF_2ed39837fea8c47e9bd3af13d16]  DEFAULT (GETUTCDATE()) FOR [created_at]
GO
ALTER TABLE [dbo].[features] ADD  CONSTRAINT [DF_394464966d0e1f8df6f3652ef1d]  DEFAULT (GETUTCDATE()) FOR [updated_at]
GO
ALTER TABLE [dbo].[mines] ADD  CONSTRAINT [DF_f6a67a6c9a9360fd4ad13bca612]  DEFAULT ((1)) FOR [is_active]
GO
ALTER TABLE [dbo].[mines] ADD  CONSTRAINT [DF_7f61a162a297a10a4e59f058926]  DEFAULT (GETUTCDATE()) FOR [created_at]
GO
ALTER TABLE [dbo].[mines] ADD  CONSTRAINT [DF_a74189173530688f5e13b9874e4]  DEFAULT (GETUTCDATE()) FOR [updated_at]
GO
ALTER TABLE [dbo].[role_feature_access] ADD  CONSTRAINT [DF_a1e0d70e5b536dd7f9d05ee0adc]  DEFAULT ((1)) FOR [has_read_access]
GO
ALTER TABLE [dbo].[role_feature_access] ADD  CONSTRAINT [DF_c9aa855d01f744230878ecd5152]  DEFAULT ((1)) FOR [has_write_access]
GO
ALTER TABLE [dbo].[role_feature_access] ADD  CONSTRAINT [DF_250e5ad0bb6ee45746e91723d87]  DEFAULT (GETUTCDATE()) FOR [created_at]
GO
ALTER TABLE [dbo].[role_feature_access] ADD  CONSTRAINT [DF_f7419a178a00e885e7be3d6377c]  DEFAULT (GETUTCDATE()) FOR [updated_at]
GO
ALTER TABLE [dbo].[roles] ADD  CONSTRAINT [DF_e5a52fc6f7a8dae64f645b09146]  DEFAULT (GETUTCDATE()) FOR [created_at]
GO
ALTER TABLE [dbo].[roles] ADD  CONSTRAINT [DF_8651ace7d160b9cf59cd0e0e2ef]  DEFAULT (GETUTCDATE()) FOR [updated_at]
GO
ALTER TABLE [dbo].[users] ADD  CONSTRAINT [DF_20c7aea6112bef71528210f631d]  DEFAULT ((1)) FOR [is_active]
GO
ALTER TABLE [dbo].[users] ADD  CONSTRAINT [DF_c5ffe390a627e760cc3d9442d7b]  DEFAULT ((0)) FOR [is_delete]
GO
ALTER TABLE [dbo].[users] ADD  CONSTRAINT [DF_c9b5b525a96ddc2c5647d7f7fa5]  DEFAULT (GETUTCDATE()) FOR [created_at]
GO
ALTER TABLE [dbo].[users] ADD  CONSTRAINT [DF_6d596d799f9cb9dac6f7bf7c23c]  DEFAULT (GETUTCDATE()) FOR [updated_at]
GO
ALTER TABLE [dbo].[users_mines] ADD  CONSTRAINT [DF_9b03231b2436ef4f1fafe95ce75]  DEFAULT ((1)) FOR [is_active]
GO
ALTER TABLE [dbo].[users_mines] ADD  CONSTRAINT [DF_c904226cf2e900c6df9c22a2414]  DEFAULT (GETUTCDATE()) FOR [created_at]
GO
ALTER TABLE [dbo].[users_mines] ADD  CONSTRAINT [DF_be2a13097ebb70942cb26de61e5]  DEFAULT (GETUTCDATE()) FOR [updated_at]
GO
ALTER TABLE [dbo].[users_roles] ADD  CONSTRAINT [DF_fd74ecddc43cc5ea78aa5f770d3]  DEFAULT ((1)) FOR [is_active]
GO
ALTER TABLE [dbo].[users_roles] ADD  CONSTRAINT [DF_a2dbc2fc7e8ad4420ee823a56c8]  DEFAULT (GETUTCDATE()) FOR [created_at]
GO
ALTER TABLE [dbo].[users_roles] ADD  CONSTRAINT [DF_8473e4a31251a6e9c0f9b5e611b]  DEFAULT (GETUTCDATE()) FOR [updated_at]
GO
ALTER TABLE [dbo].[watchlist_items] ADD  CONSTRAINT [DF_c62535578ddce16cf4b00dfde14]  DEFAULT (GETUTCDATE()) FOR [created_at]
GO
ALTER TABLE [dbo].[watchlist_items] ADD  CONSTRAINT [DF_48e80fcb02148abe53acfc50a8e]  DEFAULT (GETUTCDATE()) FOR [updated_at]
GO
ALTER TABLE [dbo].[features]  WITH CHECK ADD  CONSTRAINT [FK_dd72c34524971332eb3c61d5968] FOREIGN KEY([category_id])
REFERENCES [dbo].[categories] ([id])
GO
ALTER TABLE [dbo].[features] CHECK CONSTRAINT [FK_dd72c34524971332eb3c61d5968]
GO
ALTER TABLE [dbo].[mines]  WITH CHECK ADD  CONSTRAINT [FK_1ab5d74a704ea10f6502a35517e] FOREIGN KEY([company_id])
REFERENCES [dbo].[companies] ([id])
GO
ALTER TABLE [dbo].[mines] CHECK CONSTRAINT [FK_1ab5d74a704ea10f6502a35517e]
GO
ALTER TABLE [dbo].[role_feature_access]  WITH CHECK ADD  CONSTRAINT [FK_18c5f58c0cda993fbd8c8ea9252] FOREIGN KEY([role_id])
REFERENCES [dbo].[roles] ([id])
GO
ALTER TABLE [dbo].[role_feature_access] CHECK CONSTRAINT [FK_18c5f58c0cda993fbd8c8ea9252]
GO
ALTER TABLE [dbo].[role_feature_access]  WITH CHECK ADD  CONSTRAINT [FK_de97a2b99df888398a8ddd1baa5] FOREIGN KEY([feature_id])
REFERENCES [dbo].[features] ([id])
GO
ALTER TABLE [dbo].[role_feature_access] CHECK CONSTRAINT [FK_de97a2b99df888398a8ddd1baa5]
GO
ALTER TABLE [dbo].[roles]  WITH CHECK ADD  CONSTRAINT [FK_4bc1204a05dde26383e3955b0a1] FOREIGN KEY([company_id])
REFERENCES [dbo].[companies] ([id])
GO
ALTER TABLE [dbo].[roles] CHECK CONSTRAINT [FK_4bc1204a05dde26383e3955b0a1]
GO
ALTER TABLE [dbo].[users]  WITH CHECK ADD  CONSTRAINT [FK_7ae6334059289559722437bcc1c] FOREIGN KEY([company_id])
REFERENCES [dbo].[companies] ([id])
GO
ALTER TABLE [dbo].[users] CHECK CONSTRAINT [FK_7ae6334059289559722437bcc1c]
GO
ALTER TABLE [dbo].[users_mines]  WITH CHECK ADD  CONSTRAINT [FK_34074ef86a47ce321f50952f6c1] FOREIGN KEY([mine_id])
REFERENCES [dbo].[mines] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[users_mines] CHECK CONSTRAINT [FK_34074ef86a47ce321f50952f6c1]
GO
ALTER TABLE [dbo].[users_mines]  WITH CHECK ADD  CONSTRAINT [FK_b574dc457e0eed3cae44f2b255a] FOREIGN KEY([user_id])
REFERENCES [dbo].[users] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[users_mines] CHECK CONSTRAINT [FK_b574dc457e0eed3cae44f2b255a]
GO
ALTER TABLE [dbo].[users_roles]  WITH CHECK ADD  CONSTRAINT [FK_1cf664021f00b9cc1ff95e17de4] FOREIGN KEY([role_id])
REFERENCES [dbo].[roles] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[users_roles] CHECK CONSTRAINT [FK_1cf664021f00b9cc1ff95e17de4]
GO
ALTER TABLE [dbo].[users_roles]  WITH CHECK ADD  CONSTRAINT [FK_e4435209df12bc1f001e5360174] FOREIGN KEY([user_id])
REFERENCES [dbo].[users] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[users_roles] CHECK CONSTRAINT [FK_e4435209df12bc1f001e5360174]
GO
ALTER TABLE [dbo].[watchlist_items]  WITH CHECK ADD  CONSTRAINT [FK_0072d2b5c5969c239be193df141] FOREIGN KEY([user_id])
REFERENCES [dbo].[users] ([id])
GO
ALTER TABLE [dbo].[watchlist_items] CHECK CONSTRAINT [FK_0072d2b5c5969c239be193df141]
GO
ALTER TABLE [dbo].[watchlist_items]  WITH CHECK ADD  CONSTRAINT [FK_10bc1a47ca6016ab0254937b1be] FOREIGN KEY([miner_id])
REFERENCES [dbo].[miners] ([id])
GO
ALTER TABLE [dbo].[watchlist_items] CHECK CONSTRAINT [FK_10bc1a47ca6016ab0254937b1be]
GO

/****** Object:  Table [dbo].[users_date_selection]    Script Date: 4/17/2024 2:30:35 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[users_date_selection](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[user_id] [int] NOT NULL,
	[type] [varchar](500) NULL,
	[predefined_date] [varchar](50) NULL,
	[start_date] [varchar](200) NULL,
	[end_date] [varchar](200) NULL,
	[created_at] [datetime2](7) NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_users_date_selection_user_id] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO




CREATE TABLE sections (
  id bigint IDENTITY(1,1),
  mine_id int NOT NULL , 
  ext_section_id int NOT NULL UNIQUE,
  ext_section_name VARCHAR(128) NOT NULL,
  created_at datetime2 NOT NULL,
  updated_at datetime2 NOT NULL,
  is_active bit NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_section_mine_id FOREIGN KEY (mine_id) REFERENCES mines(id)
);


CREATE TABLE miner_activity (
  id bigint IDENTITY(1,1),
  miner_id bigint NOT NULL,
  section_id bigint DEFAULT NULL,
  shift_id int NOT NULL,
  ground_status varchar(5) DEFAULT NULL,
  comm_status int DEFAULT NULL,
  last_heard bigint NOT NULL,
  version_stamp bigint DEFAULT NULL,
  created_at datetime2 DEFAULT NULL,
  updated_at datetime2 DEFAULT NULL,
  is_active bit NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_ma_miner_id FOREIGN KEY (miner_id) REFERENCES miners (id),
  CONSTRAINT fk_ma_section_id FOREIGN KEY (section_id) REFERENCES sections (id)
  --CONSTRAINT fk_shift_id FOREIGN KEY (shift_id) REFERENCES shifts (id)
);


CREATE TABLE miner_status (
  id bigint IDENTITY(1,1),
  miner_id bigint NOT NULL,
  section_id bigint DEFAULT NULL,
  shift_id int NOT NULL,
  version_stamp bigint DEFAULT NULL,
  msg_timestamp bigint NOT NULL,
  ext_node_id bigint NOT NULL,
  --ext_section_id bigint DEFAULT NULL,
  check_in datetime2 NOT NULL,
  went_ug datetime2 DEFAULT NULL,
  went_ag datetime2 DEFAULT NULL,
  section_arrive datetime2 DEFAULT NULL,
  section_left datetime2 DEFAULT NULL,
  travel_to time DEFAULT NULL,
  travel_from time DEFAULT NULL,
  on_section time DEFAULT NULL,
  last_section bigint DEFAULT NULL,
  last_ug datetime2 DEFAULT NULL,
  created_at datetime2 DEFAULT NULL,
  updated_at datetime2 DEFAULT NULL,
  is_active bit NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_ms_miner_id FOREIGN KEY (miner_id) REFERENCES miners (id),
  CONSTRAINT fk_ms_section_id FOREIGN KEY (section_id) REFERENCES sections (id),
  --CONSTRAINT fk_shift_id FOREIGN KEY (shift_id) REFERENCES shifts (id)
  CONSTRAINT fk_last_section_id FOREIGN KEY (last_section) REFERENCES sections (id)
);


CREATE TABLE shifts (
  id bigint IDENTITY(1,1),
  ext_shift_id bigint NOT NULL,
  mine_id int NOT NULL,
  shift_name varchar(128) DEFAULT NULL,
  start_time time NOT NULL,
  end_time time NOT NULL,
  created_at datetime2 DEFAULT NULL,
  updated_at datetime2 DEFAULT NULL,
  is_active bit NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_shifts_mine_id FOREIGN KEY (mine_id) REFERENCES mines (id)
);


ALTER TABLE [dbo].[users_date_selection]
ADD CONSTRAINT FK_users_date_selection_user_id
FOREIGN KEY (user_id)
REFERENCES [dbo].[users] (id);



CREATE TABLE cuts(
id	bigint IDENTITY(1,1),
form_id	bigint NOT NULL,
mine_id	int NOT NULL,
section_id	bigint NOT NULL,
shift_id	bigint NOT NULL,
version_stamp bigint NOT NULL,
form_date	date NOT NULL,
timezone_offset int NOT NULL,
no_of_crew int DEFAULT NULL,
total_feet_mined float DEFAULT NULL,
total_cuts int DEFAULT NULL,

total_down_time time DEFAULT NULL,
last_down_time time DEFAULT NULL,
total_down_events int DEFAULT NULL,

foreman varchar(128) DEFAULT NULL,
time_in time DEFAULT NULL,
arrive_time time DEFAULT NULL,
on_coal_time time DEFAULT NULL,
quit_time time DEFAULT NULL,
incidents varchar(5) DEFAULT NULL,
no_of_crew_off int DEFAULT NULL,
end_of_track_x_cut varchar(128) DEFAULT NULL,
total_feet_bolted float DEFAULT NULL,
total_no_of_cars int DEFAULT NULL,
cm_bits_used int DEFAULT NULL,

end_hour int DEFAULT NULL,
cumulative_sum float DEFAULT NULL,

cut_id int DEFAULT NULL,
place	varchar(128) DEFAULT NULL,
height_of_cut	float DEFAULT NULL,
start_time time DEFAULT NULL,
end_time	time DEFAULT NULL,
dt_start_time	datetime2 DEFAULT NULL,
dt_end_time	datetime2 DEFAULT NULL,
start_depth	float DEFAULT NULL,
end_depth	float DEFAULT NULL,
total_feet	float DEFAULT NULL,
no_of_cars	int DEFAULT NULL,
cut_status varchar(128) DEFAULT NULL,
form_created_at	datetime2 NOT NULL,
form_saved_at	datetime2 DEFAULT NULL,
form_submitted_at	datetime2 DEFAULT NULL,
ss_end_hour	int DEFAULT NULL,
created_at	datetime2 NOT NULL,
updated_at	datetime2 NOT NULL,
is_active	int NOT NULL,
PRIMARY KEY (id),
CONSTRAINT fk_cuts_mine_id FOREIGN KEY (mine_id) REFERENCES mines (id),
CONSTRAINT fk_cuts_section_id FOREIGN KEY (section_id) REFERENCES sections (id),
CONSTRAINT fk_cuts_shift_id FOREIGN KEY (shift_id) REFERENCES shifts (id)
);



CREATE TABLE down_events(
id	bigint IDENTITY(1,1),
form_id	bigint NOT NULL,
mine_id	int NOT NULL,
section_id	bigint NOT NULL,
shift_id	bigint NOT NULL,
version_stamp bigint NOT NULL,
form_date	date NOT NULL,
timezone_offset int NOT NULL,
no_of_crew int DEFAULT NULL,
total_feet_mined float DEFAULT NULL,
total_cuts int DEFAULT NULL,
total_down_time time DEFAULT NULL,
last_down_time time DEFAULT NULL,
total_down_events int DEFAULT NULL,

foreman varchar(128) DEFAULT NULL,
time_in time DEFAULT NULL,
arrive_time time DEFAULT NULL,
on_coal_time time DEFAULT NULL,
quit_time time DEFAULT NULL,
incidents varchar(5) DEFAULT NULL,
no_of_crew_off int DEFAULT NULL,
end_of_track_x_cut varchar(128) DEFAULT NULL,
total_feet_bolted float DEFAULT NULL,
total_no_of_cars int DEFAULT NULL,
cm_bits_used int DEFAULT NULL,

down_event_id int DEFAULT NULL,
down_event_type varchar(128) DEFAULT NULL,
time_down time DEFAULT NULL,
time_repaired time DEFAULT NULL,
dt_start_time datetime2 DEFAULT NULL,
dt_end_time	datetime2 DEFAULT NULL,
duration_of_delay time DEFAULT NULL,
actual_production_lost_time time DEFAULT NULL,
equipment_identification varchar(128) DEFAULT NULL,
reason_for_downtime varchar(128) DEFAULT NULL,
down_event_status varchar(128) DEFAULT NULL,
form_created_at	datetime2 NOT NULL,
form_saved_at	datetime2 DEFAULT NULL,
form_submitted_at	datetime2 DEFAULT NULL,
created_at	datetime2 NOT NULL,
updated_at	datetime2 NOT NULL,
is_active	int NOT NULL,
PRIMARY KEY (id),
CONSTRAINT fk_de_mine_id FOREIGN KEY (mine_id) REFERENCES mines (id),
CONSTRAINT fk_de_cuts_section_id FOREIGN KEY (section_id) REFERENCES sections (id),
CONSTRAINT fk_de_cuts_shift_id FOREIGN KEY (shift_id) REFERENCES shifts (id)
);
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[production_goals](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[section_id] [bigint] NOT NULL,
	[shift_id] [bigint] NOT NULL,
	[goal] [int] NOT NULL,
	[effective_date] [date] NOT NULL,
	[end_date] [date] NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_93e5c2b1cb48adc46d43ab73932] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[production_goals] ADD  CONSTRAINT [DF_9c1118f4cddac80b3f78b291eae]  DEFAULT (getdate()) FOR [created_at]
GO

ALTER TABLE [dbo].[production_goals] ADD  CONSTRAINT [DF_ec3c02bc347ad08115ca123dbe0]  DEFAULT (getdate()) FOR [updated_at]
GO

ALTER TABLE [dbo].[production_goals]  WITH CHECK ADD  CONSTRAINT [FK_production_goals_section_id] FOREIGN KEY([section_id])
REFERENCES [dbo].[sections] ([id])
GO

ALTER TABLE [dbo].[production_goals] CHECK CONSTRAINT [FK_production_goals_section_id]
GO

ALTER TABLE [dbo].[production_goals]  WITH CHECK ADD  CONSTRAINT [FK_production_goals_shift_id] FOREIGN KEY([shift_id])
REFERENCES [dbo].[shifts] ([id])
GO

ALTER TABLE [dbo].[production_goals] CHECK CONSTRAINT [FK_production_goals_shift_id]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[audit_log](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[create_date] [datetime] NULL,
	[mine_name] [nvarchar](250) NULL,
	[created_by] [nvarchar](250) NULL,
	[created_by_role] [nvarchar](50) NULL,
	[created_for] [nvarchar](250) NULL,
	[created_for_role] [nvarchar](50) NULL,
	[action] [nvarchar](500) NULL
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[audit_log] ADD  DEFAULT (getutcdate()) FOR [create_date]
GO



/****** Object:  Table [dbo].[timezones]    Script Date: 07-08-2024 12:26:53 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[timezones](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[code] [nvarchar](255) NOT NULL,
	[is_active] [bit] NOT NULL,
	[abbreviation] [nvarchar](10) NULL,
 CONSTRAINT [PK_589871db156cc7f92942334ab7e] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UQ_b6248bf632ed7b73d6e96a26210] UNIQUE NONCLUSTERED 
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[timezones] ADD  CONSTRAINT [DF_3265a9732ba19540cac7cb70420]  DEFAULT ((1)) FOR [is_active]
GO



ALTER TABLE [dbo].[mines]
ADD [timezone_id] INT;
              
ALTER TABLE [dbo].[mines]  WITH CHECK ADD  CONSTRAINT [FK_mines_timezone_id] FOREIGN KEY([timezone_id])
REFERENCES [dbo].[timezones] ([id])
GO

/****** Object:  Table [dbo].[form_categories]    Script Date: 6/10/2025 4:37:44 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[form_categories](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[mine_id] [int] NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_d1bf30396157668a77ecf8ee717] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UQ_3dca5f522ffcfd6d3ead358fa91] UNIQUE NONCLUSTERED 
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[form_categories] ADD  CONSTRAINT [DF_f674e281717157e204710fd417d]  DEFAULT (getdate()) FOR [created_at]
GO

ALTER TABLE [dbo].[form_categories] ADD  CONSTRAINT [DF_f220345756cb09e011554de547b]  DEFAULT (getdate()) FOR [updated_at]
GO

/****** Object:  Table [dbo].[form_templates]    Script Date: 6/10/2025 4:38:16 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[form_templates](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[mine_id] [int] NOT NULL,
	[form_category_id] [int] NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[description] [nvarchar](255) NOT NULL,
	[is_delete] [bit] NOT NULL,
	[created_at] [datetime2](7) NOT NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NOT NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_dda93f70be71cb4a2e496b5ae49] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[form_templates] ADD  CONSTRAINT [DF_form_templates_is_delete]  DEFAULT ((0)) FOR [is_delete]
GO

ALTER TABLE [dbo].[form_templates] ADD  CONSTRAINT [DF_056bbcb90999a2d3a9adada5ef6]  DEFAULT (getdate()) FOR [created_at]
GO

ALTER TABLE [dbo].[form_templates] ADD  CONSTRAINT [DF_c113700adf47a9b9cfdd899615e]  DEFAULT (getdate()) FOR [updated_at]
GO

ALTER TABLE [dbo].[form_templates]  WITH CHECK ADD  CONSTRAINT [FK_dff8a8600244aa86a18d6a50794] FOREIGN KEY([form_category_id])
REFERENCES [dbo].[form_categories] ([id])
GO

ALTER TABLE [dbo].[form_templates] CHECK CONSTRAINT [FK_dff8a8600244aa86a18d6a50794]
GO

/****** Object:  Table [dbo].[form_template_definitions]    Script Date: 6/10/2025 4:38:41 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[form_template_definitions](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[mine_id] [int] NOT NULL,
	[form_template_id] [int] NOT NULL,
	[definition] [varchar](max) NOT NULL,
	[is_published] [bit] NOT NULL,
	[published_at] [datetime] NULL,
	[published_by] [int] NULL,
	[major] [tinyint] NOT NULL,
	[minor] [tinyint] NOT NULL,
	[revision] [tinyint] NOT NULL,
	[is_delete] [bit] NOT NULL,
	[created_at] [datetime2](7) NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_c52ef0bf36482130ca9e9edb04c] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[form_template_definitions] ADD  CONSTRAINT [DF_form_template_definitions_is_delete]  DEFAULT ((0)) FOR [is_delete]
GO

ALTER TABLE [dbo].[form_template_definitions] ADD  CONSTRAINT [DF_form_template_definitions_is_published]  DEFAULT ((0)) FOR [is_published]
GO

ALTER TABLE [dbo].[form_template_definitions] ADD  CONSTRAINT [DF_a90cdd3053ef80793ed918e76d5]  DEFAULT (getdate()) FOR [created_at]
GO

ALTER TABLE [dbo].[form_template_definitions] ADD  CONSTRAINT [DF_ff9b06a76eb60cbff468c01ddd0]  DEFAULT (getdate()) FOR [updated_at]
GO

ALTER TABLE [dbo].[form_template_definitions]  WITH CHECK ADD  CONSTRAINT [FK_919cfcc51a114aa08d9c928dab3] FOREIGN KEY([form_template_id])
REFERENCES [dbo].[form_templates] ([id])
GO

ALTER TABLE [dbo].[form_template_definitions] CHECK CONSTRAINT [FK_919cfcc51a114aa08d9c928dab3]
GO

/****** Object:  Table [dbo].[forms]    Script Date: 6/10/2025 4:39:02 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[forms](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[mine_id] [int] NOT NULL,
	[form_template_id] [int] NOT NULL,
	[form_template_definition_id] [int] NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[content] [varchar](max) NOT NULL,
	[submission] [varchar](max) NULL,
	[is_submitted] [bit] NOT NULL,
	[submitted_at] [datetime] NULL,
	[submitted_by] [int] NULL,
	[is_processed] [bit] NOT NULL,
	[is_delete] [bit] NOT NULL,
	[created_at] [datetime2](7) NULL,
	[created_by] [int] NULL,
	[updated_at] [datetime2](7) NULL,
	[updated_by] [int] NULL,
 CONSTRAINT [PK_ba062fd30b06814a60756f233da] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[forms] ADD  DEFAULT ((0)) FOR [is_submitted]
GO

ALTER TABLE [dbo].[forms] ADD  CONSTRAINT [DF_forms_is_delete]  DEFAULT ((0)) FOR [is_delete]
GO

ALTER TABLE [dbo].[forms] ADD  CONSTRAINT [DF_forms_is_processed]  DEFAULT ((0)) FOR [is_processed]
GO

ALTER TABLE [dbo].[forms] ADD  CONSTRAINT [DF_493f23fbe8b96b6543b0a3c57e7]  DEFAULT (getdate()) FOR [created_at]
GO

ALTER TABLE [dbo].[forms] ADD  CONSTRAINT [DF_d7778047a6fd084579199edddfe]  DEFAULT (getdate()) FOR [updated_at]
GO

ALTER TABLE [dbo].[forms]  WITH CHECK ADD  CONSTRAINT [FK_22b3e43e6e80894550527c5db08] FOREIGN KEY([form_template_definition_id])
REFERENCES [dbo].[form_template_definitions] ([id])
GO

ALTER TABLE [dbo].[forms] CHECK CONSTRAINT [FK_22b3e43e6e80894550527c5db08]
GO

ALTER TABLE [dbo].[forms]  WITH CHECK ADD  CONSTRAINT [FK_50cbc3dc89fe9733b4be921f5d0] FOREIGN KEY([form_template_id])
REFERENCES [dbo].[form_templates] ([id])
GO

ALTER TABLE [dbo].[forms] CHECK CONSTRAINT [FK_50cbc3dc89fe9733b4be921f5d0]
GO