import {
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsString,
  Length,
} from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateFormCategoryDto {
  @IsString({ message: 'Name must be a string' })
  @Length(1, 100, {
    message: 'Name length must be between 1 and 100 characters',
  })
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  createdBy: number;
}
