
import { DndContext, DragEndEvent, DragOverEvent, DragOverlay, DragStartEvent, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { useEffect, useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getFormTemplateWithDefinition } from '../../../../api/forms/formtemplateapis';
import { useParams } from 'react-router-dom';
import Loader from '../../common/Loader';
import FormPreview from './preview/FormPreview';
import FormTools from './FormTools';
import FormProperties from './properties/FormProperties';
import { arrayMove } from "@dnd-kit/sortable";
import { Group, Id, Item, SelectedItem, Tool, } from '../types';
import { createPortal } from 'react-dom';
import FormGroupOverlay from './FormGroupOverlay';
import FormItemOverlay from './FormItemOverlay';
import { useAddDefinitionRevision, useEditFormTemplateDefinition } from '../../../../services/mutations/formtemplatemutations';
import { combineProperties, getDefaults } from '../utilities';
import { toast } from 'react-toastify';
import { CloseIcon } from '../../../../assets/icons/icons';

const FormDesigner = () => {
  const params = useParams();
  const addDefinitionRevision = useAddDefinitionRevision();
  const editFormTemplateDefinition = useEditFormTemplateDefinition();
  const [showPreview, setShowPreview] = useState(false);
  const [templateDetails, setTemplateDetails] = useState<any>({});
  const [selectedItem, setSelectedItem] = useState<SelectedItem>({id:''});
  const [templateHistory, setTemplateHistory] = useState<any>([]);
  const [historyIndex, setHistoryIndex] = useState(0);
	const [publishActive, setPublishActive] = useState(false);
  const [revisionCreated, setRevisionCreated] = useState(false);

  const templateProperties = useMemo(() => {
    return (historyIndex >= 0 && templateHistory.length > 0
    && templateHistory.length > historyIndex && templateHistory[historyIndex].properties)
    ? JSON.parse(JSON.stringify(templateHistory[historyIndex]?.properties))
    : {};
  }, [templateHistory, historyIndex]);

  const templateContent = useMemo(() => {
    return (historyIndex >= 0 && templateHistory.length > 0 &&
        templateHistory.length > historyIndex && templateHistory[historyIndex].content)
        ? JSON.parse(JSON.stringify(templateHistory[historyIndex].content))
        : {groups: []};
  }, [templateHistory, historyIndex]);

  const groups = useMemo(() => {
    if(!templateContent.groups) {
      return [];
    }
    return JSON.parse(JSON.stringify(templateContent.groups)) ?? [];
  }, [templateContent]);

  const items = useMemo(() => {
    return groups.map((grp: Group) => {
      return (
        grp.items.map((item: Item) => {
          return item;
        })
      );
    }).flat();
  }, [groups]);

  const setNewContent = (newGroups:Group[], newSelectedItem?:SelectedItem) => {
    let newContent = JSON.parse(JSON.stringify(templateContent));
    newContent.groups = newGroups ?? newContent.groups ?? [];
    
    handleUpdateContent(newContent, newSelectedItem);
  };

	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 3,
			}
		})
	);

	const [activeGroup, setActiveGroup] = useState<Group | null>(null);
	const [activeItem, setActiveItem] = useState<Item | null>(null);
	const [activeTool, setActiveTool] = useState<Tool | null>(null);

  const [canUndoButton, canRedoButton] = useMemo(() => {
    return [
      historyIndex > 0 && templateHistory.length > 1,
      historyIndex >= 0 && historyIndex < templateHistory.length - 1,
    ];
  }, [historyIndex, templateHistory]);

  const { status, data: formTemplate, isLoading } = useQuery({
    queryKey: ['form-template'],
    queryFn: () => getFormTemplateWithDefinition(params?.formTemplateId),
    refetchOnWindowFocus: true,
  });
  
  useEffect(() => {
    if(formTemplate?.data) {
      setTemplateDetails({
        id: formTemplate?.data?.formTemplateId,
        definitionId: formTemplate?.data?.formTemplateDefinitionId,
        name: formTemplate?.data?.name,
        description: formTemplate?.data?.description,
        categoryId: formTemplate?.data?.formCategoryId,
        category: formTemplate?.data?.category,
        major: formTemplate?.data?.major,
        minor: formTemplate?.data?.minor,
        revision: formTemplate?.data?.revision,
      });
      
      if(formTemplate?.data?.definition) {
        const definition = JSON.parse(formTemplate?.data?.definition) ?? {};
        definition.properties = definition?.properties ?? {};
        definition.content = definition?.content ?? {};
        if(templateHistory.length > 0) {
          const currentHistory = JSON.parse(JSON.stringify(templateHistory));
          const updatedHistory = currentHistory.slice(0,historyIndex);
          updatedHistory.push(definition);
          setTemplateHistory(updatedHistory);
        }
        else {
          setTemplateHistory([definition]);
          setHistoryIndex(0);
        }
      }
      setPublishActive(!formTemplate?.data?.isPublished);
    }
  }, [formTemplate?.data]);

  async function storeUpdatedTemplate(template:any) {
    const data:any = {};
    data.id = templateDetails.definitionId;
    data.definition = JSON.stringify(template);

    if(!revisionCreated) {
      try {
        const res:any = await addDefinitionRevision.mutateAsync(data);
        if(res?.status == 200 || res?.status == 201) {
          if(res?.data?.id) {
            setTemplateDetails({
              ...templateDetails,
              definitionId: res.data.id
            });
          }
          setRevisionCreated(true);
          setPublishActive(true);
        }
      } catch(err: any) {
        toast.success('There was an issue saving the form.');
        setPublishActive(false);
        console.log(err);
      }
    }
    else {
      try {
        const res:any = await editFormTemplateDefinition.mutateAsync(data);
        if(res?.status == 200 || res?.status == 201) {
          setPublishActive(true);
        }
      } catch(err: any) {
        toast.success('There was an issue saving the form.');
        setPublishActive(false);
        console.log(err);
      }
    }
  };

	function createNewGroup() {
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const newId = `group-${currentGroups.length}`;
    
		const groupToAdd: Group = {
			id: newId,
			name: newId,
			index: currentGroups.length,
			items: [],
			properties: getDefaults('Group'),
      questions: [],
		}
    
    const newGroups = [...currentGroups, groupToAdd];
    const newSelectedItem = {id: newId, group: groupToAdd};
		setNewContent(newGroups, newSelectedItem);
	}

	function deleteGroup(id:Id) {
		const filteredGroups = groups.filter((grp:Group) => grp.id !== id);
    const updatedGroups = filteredGroups.map((grp:Group, index:number) => {
      const newId = `group-${index}`;
      return {
        ...grp,
        id: newId,
        index: index,
      };
    });
		setNewContent(updatedGroups, {id:''});
	}

	function createNewItem(groupId: Id, toolType?:any, overIndex?:number) {
    const updatedGroups = JSON.parse(JSON.stringify(groups));
    const groupIndex = updatedGroups.findIndex((grp:Group) => grp.id === groupId);
    const updatedGroup = updatedGroups[groupIndex];

    const newId = `${groupId}-item-${updatedGroup.items.length}`;
    let type = toolType ?? 'Input';

		const itemToAdd: Item = {
			id: newId,
			index: updatedGroup.items.length,
			groupId,
			content: '',
			properties: getDefaults(type),
		}
    if(overIndex) {
      const newItems = updatedGroup.items;
      newItems.splice(overIndex, 0, itemToAdd);
      
      const updatedItems = newItems.map((itm:Group, index:number) => {
        const newId = `${groupId}-item-${index}`;
        return {
          ...itm,
          id: newId,
          index: index,
        };
      });
      updatedGroup.items = updatedItems;
    }
    else {
      updatedGroup.items.push(itemToAdd);
    }

    const newSelectedItem = {id: newId, item: itemToAdd};
    setNewContent(updatedGroups, newSelectedItem);
	}

	function cloneItem(item: Item) {
    const updatedGroups = JSON.parse(JSON.stringify(groups));
    const groupIndex = updatedGroups.findIndex((grp:Group) => grp.id === item.groupId);
    const updatedGroup = updatedGroups[groupIndex];

    const newId = `${item.groupId}-item-${updatedGroup.items.length}`;
    let newProperties = item.properties ?? getDefaults('Input');
    if(newProperties.key) {
      let guid = crypto.randomUUID().toUpperCase();
      newProperties.key = guid;
    }
    if(newProperties.text) { newProperties.text = newProperties.type; }
    if(newProperties.label) { newProperties.label = newProperties.type; }
		const itemToAdd: Item = {
			id: newId,
			index: updatedGroup.items.length,
			groupId: item.groupId,
			content: item.content ?? '',
			properties: newProperties,
		}
    updatedGroup.items.push(itemToAdd);

    const newSelectedItem = {id: newId, item: itemToAdd};
    setNewContent(updatedGroups, newSelectedItem);
	}

	function deleteItem(item: Item) {
    const updatedGroups = JSON.parse(JSON.stringify(groups));
    const groupIndex = updatedGroups.findIndex((grp:Group) => grp.id === item.groupId);
    const updatedGroup = updatedGroups[groupIndex];
    const filteredItems = updatedGroup.items.filter((itm:Item) => itm.id !== item.id);

    const updatedItems = filteredItems.map((itm:Group, index:number) => {
      const newId = `group-${updatedGroup.length}-item-${index}`;
      return {
        ...itm,
        id: newId,
        index: index,
      };
    });

    updatedGroup.items = updatedItems;
    setNewContent(updatedGroups, {id:''});
	}
  
	function handleDragStart(e: DragStartEvent) {
		if(e.active.data.current?.type === 'Group') {
			setActiveGroup(e.active.data.current.group);
			return;
		}
		if(e.active.data.current?.type === 'Item') {
			setActiveItem(e.active.data.current.item);
			return;
		}
		if(e.active.data.current?.type === 'ToolItem') {
			setActiveTool(e.active.data.current.toolItem);
			return;
		}
	}
  
	function handleDragEnd(e: DragEndEvent) {
		setActiveGroup(null);
		setActiveItem(null);
		setActiveTool(null);
		const {active, over} = e;
		if(!over) return;

		const activeId = active.id;
		const overId = over.id;

		if(activeId === overId) return;
    
    if(active.data.current?.type === 'ToolItem') {
      let type = active.data.current?.toolItem?.label ?? 'Input';
      if(over.data.current?.type === 'Group') {
        createNewItem(overId, type);
      }
      if(over.data.current?.type === 'Item') {
        const overIndex = items.findIndex((item:Item) => item.id === overId);
        createNewItem(over.data.current?.item.groupId, type, overIndex);
      }
      return;
    }

    const currentGroups = JSON.parse(JSON.stringify(groups));
		const activeIndex = currentGroups.findIndex((grp:Group) => grp.id === activeId);
		const overIndex = currentGroups.findIndex((grp:Group) => grp.id === overId);
		const updatedGroups:Group[] = arrayMove(currentGroups, activeIndex, overIndex);

		setNewContent(updatedGroups);
	}
  
	function handleDragOver(e: DragOverEvent) {
		const {active, over} = e;
		if(!over) return;

		const activeId = active.id;
		const overId = over.id;

		if(activeId === overId) return;

		const isAnActiveItem = active.data.current?.type === 'Item';
		const isOverAnItem = over.data.current?.type === 'Item';

		if(!isAnActiveItem) return;
    
    const currentItems = JSON.parse(JSON.stringify(items));
    const currentGroups = JSON.parse(JSON.stringify(groups));
		if(isAnActiveItem && isOverAnItem) {
			const activeIndex = currentItems.findIndex((item:Item) => item.id === activeId);
			const overIndex = currentItems.findIndex((item:Item) => item.id === overId);
			currentItems[activeIndex].groupId = currentItems[overIndex].groupId;
			
			const updatedItems:Item[] = arrayMove(currentItems, activeIndex, overIndex);
      const itemsByGroup:any = {};
      updatedItems.forEach((itm:Item) => {
        if(!itemsByGroup[itm.groupId]) {
          itemsByGroup[itm.groupId] = [];
        }
        const newId = `${itm.groupId}-item-${itemsByGroup[itm.groupId].length}`;
        itemsByGroup[itm.groupId].push({
          ...itm,
          id: newId,
          index: itemsByGroup[itm.groupId].length
        });
      });
      
      for(let i = 0; i < Object.keys(itemsByGroup).length; i++) {
        let key = Object.keys(itemsByGroup)[i];
        let groupIndex = currentGroups.findIndex((grp:Group) => grp.id === key);
        const updatedGroup = currentGroups[groupIndex];
        updatedGroup.items = itemsByGroup[key];
      }
			
			setNewContent(currentGroups);
		}

		const isOverAGroup = over.data.current?.type === 'Group';

		if(isAnActiveItem && isOverAGroup) {
			const activeIndex = currentItems.findIndex((item:Item) => item.id === activeId);

			currentItems[activeIndex].groupId = overId;
			
			const updatedItems:Item[] = arrayMove(currentItems, activeIndex, activeIndex);
      const itemsByGroup:any = {};
      updatedItems.forEach((itm:Item) => {
        if(!itemsByGroup[itm.groupId]) {
          itemsByGroup[itm.groupId] = [];
        }
        const newId = `${itm.groupId}-item-${itemsByGroup[itm.groupId].length}`;
        itemsByGroup[itm.groupId].push({
          ...itm,
          id: newId,
          index: itemsByGroup[itm.groupId].length
        });
      });
      
      for(let i = 0; i < Object.keys(itemsByGroup).length; i++) {
        let key = Object.keys(itemsByGroup)[i];
        let groupIndex = currentGroups.findIndex((grp:Group) => grp.id === key);
        const updatedGroup = currentGroups[groupIndex];
        updatedGroup.items = itemsByGroup[key];
      }
			
			setNewContent(currentGroups);
		}
	}

  useEffect(() => {
    const { id, group, item } = selectedItem;
    let newSelectedItem = JSON.parse(JSON.stringify(selectedItem));
    if(!(group || item)) {
      newSelectedItem = {id: '', template: {details: templateDetails, properties: templateProperties, content: templateContent }};
    }
    
    if(group) {
      const index = groups.findIndex((grp:Group) => grp.id === id);
      newSelectedItem.group = groups[index];
    }
    
    if(item) {
      const index = items.findIndex((itm:Item) => itm.id === id);
      newSelectedItem.item = items[index];
    }
    setSelectedItem(newSelectedItem);
  }, [historyIndex, templateDetails]);

  function handleUndoClick() {
    setHistoryIndex(historyIndex-1);
  }
  function handleRedoClick() {
    setHistoryIndex(historyIndex+1);
  }

  function handleUpdateContent(content:any, newSelectedItem?:SelectedItem) {
    const currentHistory = JSON.parse(JSON.stringify(templateHistory));
    const currentTemplate = JSON.parse(JSON.stringify(templateHistory[historyIndex]));
    currentTemplate.content = content;
    currentTemplate.style = currentTemplate.style ?? {};
    const updatedHistory = currentHistory.slice(0,historyIndex+1);
    updatedHistory.push(currentTemplate);
    
    if(newSelectedItem) {
      setSelectedItem(newSelectedItem);
    }
    setTemplateHistory(updatedHistory);
    setHistoryIndex(historyIndex+1);
    storeUpdatedTemplate(currentTemplate);
  }

  const updateGroup = (group: Group) => {
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const activeIndex = currentGroups.findIndex((grp:Group) => grp.id === group.id);
    currentGroups[activeIndex] = group;
    setNewContent(currentGroups);
  }

  const updateItem = (item: Item) => {
    const currentItems = JSON.parse(JSON.stringify(items));
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const activeIndex = currentItems.findIndex((itm:Item) => itm.id === item.id);
    const itemGroupIndex = currentGroups.findIndex((grp:Group) => grp.id === item.groupId);
    currentItems[activeIndex] = item;

    currentGroups[itemGroupIndex].items = currentItems.filter((itm:Item) => itm.groupId === item.groupId);
    setNewContent(currentGroups);
  }

  const replaceItem = (item: Item, type: any) => {
    const currentItems = JSON.parse(JSON.stringify(items));
    const currentGroups = JSON.parse(JSON.stringify(groups));
    const itemIndex = currentItems.findIndex((itm:Item) => itm.id === item.id);
    const activeItem = currentItems[itemIndex];
    const itemGroupIndex = currentGroups.findIndex((grp:Group) => grp.id === activeItem.groupId);
    let properties = getDefaults(type);

    activeItem.properties = combineProperties(properties, item.properties);
    
    currentItems[itemIndex] = activeItem;
    currentGroups[itemGroupIndex].items = currentItems.filter((itm:Item) => itm.groupId === activeItem.groupId);
    setNewContent(currentGroups);
  }

  return (
      <div className="w-full">
        <div className="sticky w-full top-0 z-30 agBreakup2 bg-top px-10 2xl:px-16">
          <div className="grid grid-cols-2 border-b-[1px] border-[#80c2fe] pb-1 pt-4 ">
            <div className="">
              <h6 className="pt-2 font-bold text-white text-[32px]">
                Template Designer
              </h6>
            </div>

            <div className="">
              <h6 className="pt-2 font-bold text-white text-[32px] text-right">
                Forms
              </h6>
            </div>
          </div>
        </div>

        <div className="mt-4 px-10 2xl:px-16">
          <div>
              {isLoading ? (
                <div>
                  <div>
                    <div className="flex justify-center items-center h-full pt-[200px] white">
                      {<Loader />}
                    </div>
                    <div className="flex justify-center items-center h-full text-xl pt-2 text-white">
                      Loading....
                    </div>
                  </div>
                </div>
              ) : (
                <DndContext
                  sensors={sensors}
                  onDragStart={handleDragStart}
                  onDragEnd={handleDragEnd}
                  onDragOver={handleDragOver}
                >
                  <div className="flex justify-between my-2">
                    <h6 className="font-semibold text-[20px] text-white">{templateDetails?.name}</h6>
                    <div className="float-end relative">
                      <div className="flex">
                        <div className="text-white mx-2">Show Preview</div>
                        <div
                          className={`relative w-[32px] h-[16px] rounded-full mt-1`}
                        >
                          <label className={'cursor-pointer'} title={`${showPreview ? 'Click to Hide Preview' : ' Click to Show Preview'}`}>
                            <input
                              type="checkbox"
                              tabIndex={Number('-1')}
                              className="sr-only peer"
                              checked={showPreview}
                              onChange={() => {
                                setShowPreview(!showPreview);
                              }}
                              />
                            <div
                              className={`w-full h-full bg-gray-200  bg-opacity-25 border-[0.5px] border-gray-200 outline-none peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-[162%] rtl:peer-checked:after:-translate-x-[162%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white  after:rounded-full after:h-[10px] after:w-[10px] after:my-[1px] after:mx-[2px]  after:transition-all  peer-checked:bg-[#96FB60] peer-checked:bg-opacity-25  peer-checked:border-[1px] peer-checked:border-[#96FB60]`}
                            ></div>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={`grid gap-4 grid-container relative
                    ${showPreview ? 'grid-container-wide' : 'grid-container'}
                  `}>
                    {!showPreview &&
                      <div className="p-4 col-span-1 col-start-1 grid-item grid-item-tools overflow-y-auto">
                        <FormTools
                          canUndoButton={canUndoButton}
                          canRedoButton={canRedoButton}
                          undoAction={handleUndoClick}
                          redoAction={handleRedoClick}
                        />
                      </div>
                    }
                    <div className={`
                        grid-item p-0 col-span-1
                        grid-item-form-area overflow-y-auto
                        ${!showPreview ? 'hover:ring' : ''}
                        ${selectedItem.template && !showPreview ? 'selected' : '' }
                        ${showPreview ? 'grid-item-preview col-start-1' : 'grid-item col-start-2 cursor-pointer'}
                      `}
                      onClick={() => {
                        if(!showPreview) {
                          setSelectedItem({id: '', template: {details: templateDetails, properties: templateProperties, content: templateContent }});
                        }
                      }}>
                      <FormPreview
                        showPreview={showPreview}
                        groups={groups}
                        items={items}
                        createNewGroup={createNewGroup}
                        deleteGroup={deleteGroup}
                        createNewItem={createNewItem}
                        cloneItem={cloneItem}
                        deleteItem={deleteItem}
                        selectedItem={selectedItem}
                        setSelectedItem={setSelectedItem}
                      />
                    </div>
                    {!showPreview &&
                      <div className="col-span-1 col-start-3 p-4 grid-item grid-item-properties overflow-y-auto relative">
                        {selectedItem.id && selectedItem.id.toString().length > 0 &&
                          <div className="w-full flex justify-end absolute right-4 top-2">
                            <button
                              onClick={(e) => setSelectedItem({id: '', template: {details: templateDetails, properties: templateProperties, content: templateContent }})}
                              className="opacity-60 hover:opacity-100 p-0 m-0 h-3 w-3">
                              <CloseIcon className="h-5 w-5" />
                            </button>
                          </div>
                        }
                        <FormProperties
                          selectedItem={selectedItem}
                          updateGroup={updateGroup}
                          updateItem={updateItem}
                          replaceItem={replaceItem}
                          publishActive={publishActive}
                          setPublishActive={setPublishActive}
                        />
                      </div>
                    }
                  </div>
                  {createPortal(
                    <DragOverlay>
                      {activeGroup &&
                        <FormGroupOverlay
                          group={activeGroup}
                          items={items.filter((item:Item) => item.groupId === activeGroup.id)}
                        />
                        
                      }
                      {(activeItem || activeTool) && 
                        <FormItemOverlay />
                      }
                    </DragOverlay>, document.body
                  )}
                </DndContext>
              )}
            </div>
        </div>
      </div>
  );
};

export default FormDesigner;
