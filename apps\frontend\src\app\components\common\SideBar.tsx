import { useEffect } from 'react';
import { useRef } from 'react';
import { motion } from 'framer-motion';
import { Link, useParams } from 'react-router-dom';
import { UserIcon,Logout } from '../../../assets/icons/icons';
import mixpanel from 'mixpanel-browser';

import { useNavigate } from 'react-router';
import {
  AuditLogIcon,
  ComplianceIcon,
  DashboardIcon,
  LeftArrow,
  LocationIcon,
  MineIcon,
  ProductionIcon,
  RefreshIcon,
  SafetyIcon,
  SettingIcon,
  SideBarPointLine,
  FormIcon,
} from '../../../assets/icons/icons';
import { useMediaQuery } from 'react-responsive';
import { useLocation } from 'react-router-dom';
import decodeJWT from '../../../utils/jwtdecoder';
import { Dropdown } from 'flowbite-react';
import { Tooltip } from '@material-tailwind/react';
import { useQuery } from '@tanstack/react-query';
import { getFeatures } from '../../../api/users/userapis';
import { getMine, getMineName } from '../../../api/companies/companyapis';
const Sidebar = (props: any) => {
  const isTabletMid = useMediaQuery({ query: '(max-width: 768px)' });
  const sidebarRef = useRef();
  const { pathname } = useLocation();
  const params = useParams();
  const decoded = decodeJWT();
  

  const features = props?.features;

  const { data: mineDetails } = useQuery({
    queryKey: ['minedetails', params?.mineId],
    refetchOnWindowFocus: false,
    queryFn: () => {
      if (params?.mineId) {
        return getMineName(params?.mineId);
      }
    },
    enabled: !!params?.mineId,
  });

  const navigate = useNavigate();

  function getLogo(featureName: string) {
    switch (featureName) {
      case 'Location':
        return (
          <LocationIcon
            className={`h-6 w-4 ${
              params['*']?.includes('Location') ? 'text-black' : ''
            }  ${props?.open ? '' : 'm-auto'}`}
          />
        );
      case 'Forms':
        return (
          <FormIcon
            className={`h-6 w-4 ${
              params['*']?.includes('Forms') ? 'text-black' : ''
            }  ${props?.open ? '' : 'm-auto'}`}
          />
        );
      case 'Production':
        return (
          <ProductionIcon
            className={`h-6 w-4 ${props?.open ? '' : 'm-auto'}`}
          />
        );
      case 'Compliance':
        return (
          <ComplianceIcon
            className={`h-6 w-4 ${
              params['*']?.includes('Compliance') ? 'text-black' : ''
            } ${props?.open ? '' : 'm-auto'}`}
          />
        );
      case 'Setting':
        return (
          <SettingIcon className={`h-6 w-4 ${props?.open ? '' : 'm-auto'}`} />
        );
      case 'Dashboard':
        return (
          <DashboardIcon className={`h-6 w-4 ${props?.open ? '' : 'm-auto'}`} />
        );
      case 'Mines':
        return (
          <MineIcon className={`h-6 w-4 ${props?.open ? '' : 'm-auto'}`} />
        );
      case 'Safety':
        return (
          <SafetyIcon className={`h-6 w-4 ${props?.open ? '' : 'm-auto'}`} />
        );
      default:
        return '';
    }
  }

  const superUserSettingChildern = {
    feature: 'Setting',
    logo: getLogo('Setting'),
    disabled: false,
    childerns: [
      { title: 'Users', value: 'users' },
      { title: 'Features', value: 'Features' },
      { title: 'Shifts', value: 'shifts' },
      { title: 'Goals', value: 'goals' },
    ],
  };

  const superUserEformChildern = {
    feature: 'Forms',
    logo: getLogo('Forms'),
    disabled: false,
    childerns: [
      { title: 'Templates', value: 'Templates' },
      { title: 'New', value: 'New' },
      { title: 'Completed', value: 'Completed' },
    ],
  };

  const settingChildernForUsers = {
    feature: 'Setting',
    logo: getLogo('Setting'),
    disabled: false,
    childerns: features?.data
      ?.map((ele: any) => {
        if (
          ele?.FeatureName == 'Users' ||
          ele?.FeatureName == 'Features' ||
          ele?.FeatureName == 'Goals' ||
          ele?.FeatureName == 'Shifts'
        )
          return {
            title: ele?.FeatureName,
            value: ele?.FeatureName,
          };
      })
      .filter((ele: any) => ele != undefined),
  };

  const eformChildernForUsers = {
    feature: 'Forms',
    logo: getLogo('Forms'),
    disabled: false,
    childerns: features?.data
      ?.map((ele: any) => {
        let title = ele?.FeatureName;
        let value = ele?.FeatureName;

        if(ele?.FeatureName == 'FormTemplates') {
            title = 'Templates';
        }
        if(ele?.FeatureName == 'CompletedForms') {
            title = 'Completed';
        }
        if(ele?.FeatureName == 'NewForms') {
            title = 'New';
        }
        if (
          ele?.FeatureName == 'FormTemplates' ||
          ele?.FeatureName == 'NewForms' ||
          ele?.FeatureName == 'CompletedForms'
        )
          return {
            title: title,
            value: value,
          };
      })
      .filter((ele: any) => ele != undefined),
  };

  const featureDataForSuperuser = [
    { FeatureName: 'Dashboard' },
    { FeatureName: 'Location' },
    { FeatureName: 'Production' },
    { FeatureName: 'Compliance' },
    { FeatureName: 'Safety' },
  ]
    ?.map((ele: any) => {
      return {
        feature: ele?.FeatureName,
        logo: getLogo(ele?.FeatureName),
        disabled:
          ele?.FeatureName == 'Dashboard' ||
          ele?.FeatureName == 'Safety'
            ? true
            : false,
        childerns:
          ele?.FeatureName == 'Location'
            ? [
                { title: 'Dashboard', value: 'dashboard' },
                { title: 'Live', value: 'live', subTab: 'checkins' },
                { title: 'Reports', value: 'report', subTab: 'checkins' },
              ]
            : ele?.FeatureName == 'Production'
            ? [
                { title: 'Live', value: 'live', subTab: 'mine' },
                { title: 'Reports', value: 'report', subTab: 'mine' },
              ]
            : [],
      };
    })
    ?.filter((ele: any) => Object?.keys(ele)?.length != 0);
    featureDataForSuperuser.push(superUserEformChildern);
    featureDataForSuperuser.push(superUserSettingChildern);

  const featureData = features?.data
    ?.sort((a: any, b: any) => a.FeatureId - b.FeatureId)
    ?.map((ele: any) => {
      return ele?.FeatureName == 'Users' ||
        ele?.FeatureName == 'Features' ||
        ele?.FeatureName == 'Shifts' ||
        ele?.FeatureName == 'Goals' ||
        ele?.FeatureName == 'FormTemplates' ||
        ele?.FeatureName == 'NewForms' ||
        ele?.FeatureName == 'CompletedForms'
        ? {}
        : {
            feature: ele?.FeatureName,
            logo: getLogo(ele?.FeatureName),
            disabled: ele?.FeatureName == 'Dashboard' ? true : false,
            childerns:
              ele?.FeatureName == 'Location'
                ? [
                    { title: 'Dashboard', value: 'dashboard' },
                    { title: 'Live', value: 'live', subTab: 'checkins' },
                    { title: 'Reports', value: 'report', subTab: 'checkins' },
                  ]
                : ele?.FeatureName == 'Production'
                ? [
                    { title: 'Live', value: 'live', subTab: 'mine' },
                    { title: 'Reports', value: 'report', subTab: 'mine' },
                  ]
                : [],
          };
    })
    ?.filter((ele: any) => Object?.keys(ele)?.length != 0);

  if(eformChildernForUsers?.childerns?.length != 0) {
    featureData?.push(eformChildernForUsers);
  }
  if(settingChildernForUsers?.childerns?.length != 0) {
    featureData?.push(settingChildernForUsers);
  }

  useEffect(() => {
    if (isTabletMid) {
      props?.setOpen(false);
    } else {
      props?.setOpen(true);
    }
  }, [isTabletMid]);

  useEffect(() => {
    isTabletMid && props?.setOpen(false);
  }, [pathname]);

  const Nav_animation = isTabletMid
    ? {
        open: {
          x: [null, null, null],
          width: '232px',
          transition: {
            damping: 0,
            delay: null,
          },
        },
        closed: {
          x: [null, null, null],
          width: '0px',
          transition: {
            damping: 0,
            delay: null,
          },
        },
      }
    : {
        open: {
          x: [null, null, null],
          width: '232px',
          transition: {
            damping: 0,
            delay: null,
          },
        },
        closed: {
          x: [null, null, null],
          width: '88px',
          transition: {
            damping: 0,
            delay: null,
          },
        },
      };

  if (!isTabletMid) {
    Nav_animation.open.transition.duration = 0; // Set duration to 0 for laptop screens
    Nav_animation.closed.transition.duration = 0;
  } else {
    Nav_animation.open.transition.duration = null;
    Nav_animation.closed.transition.duration = null;
  }

  return (
    <div>
      <div
        onClick={() => props?.setOpen(false)}
        className={`md:hidden fixed inset-0  z-[998] bg-black/50 ${
          props?.open ? 'block' : 'hidden'
        } `}
      ></div>
      <motion.div
        ref={sidebarRef}
        variants={Nav_animation}
        initial={{ x: isTabletMid ? 0 : 0 }}
        animate={props?.open ? 'open' : 'closed'}
        className={` bg-[#1A252F] text-white shadow-xl z-[999]  
           ${
             props?.open ? 'overflow-hidden' : ''
           } md:relative fixed h-[calc(100vh-3rem)]  
        `}
      >
        <div
          className={`flex flex-col justify-between pt-[72px] pr-2.5 ${
            props?.open ? 'hover:overflow-y-auto  overflow-hidden ' : ''
          } h-[85vh]  2xl:h-[82vh]  ${
            isTabletMid ? `${props?.open ? '' : 'hidden'}` : ``
          }`}
        >
          {decodeJWT()?.role == 'superuser' ? (
            <div>
              <Dropdown
                label=""
                placement="right-start"
                className={`z-50 text-white ${
                  props?.open
                    ? 'border-none bg-transparent border-none'
                    : 'bg-[#1A252F] border-[#1A252F] '
                }   ${' transformDropdownProd w-fit'} `}
                dismissOnClick={false}
                trigger="hover"
                renderTrigger={() => (
                  <div>
                    <Link
                      to={params?.mineId ? '#' : '/app/Mines'}
                      className={
                        params?.mineId ? `cursor-auto` : `cursor-pointer`
                      }
                    >
                      <Tooltip
                        content={'Mines'}
                        placement="right"
                        arrow={true}
                        className={
                          props?.open
                            ? 'bg-transparent text-transparent'
                            : 'bg-transparent text-transparent'
                        }
                      >
                        <div
                          className={
                            params['*']?.includes('Mines')
                              ? `flex bg-white text-black font-normal border border-white rounded-lg px-2 py-2  text-gray-600  mx-3 ${
                                  props?.open ? 'w-[151px]' : ''
                                }`
                              : `flex font-medium  rounded-lg px-2 py-2   mx-3`
                          }
                        >
                          {getLogo('Mines')}
                          {props.open ? (
                            <span className="px-2">{'Mines'}</span>
                          ) : (
                            ''
                          )}
                          {params?.mineId && props?.open ? (
                            <button 
                              className={
                                !params?.mineId
                                  ? `cursor-auto`
                                  : `cursor-pointer`
                              }
                              title="Refresh to go back on mines"
                              onClick={() => {
                                navigate('/app/Mines');
                                sessionStorage.clear();
                              }}
                            >
                              <RefreshIcon className="h-6 w-4 ml-[24px]" />
                            </button>
                          ) : (
                            ''
                          )}
                        </div>
                      </Tooltip>
                    </Link>
                    {props?.open && params?.mineId && (
                      <div className="py-1 pt-2 ml-4 ">
                        <div className="relative top-[-10px] left-[-6px]  z-[-999] h-[1vh]">
                          <SideBarPointLine className="w-5 h-10" />
                        </div>
                        <div className="">
                          <h1
                            title={mineDetails?.data?.name}
                            id="Dashboard"
                            className={`font-normal  underline cursor-auto text-[#FFB132] rounded-lg px-3 py-2 text-[16px] ml-2`}
                          >
                            {mineDetails?.data?.name}
                          </h1>
                        </div>
                        {featureDataForSuperuser?.map(
                          (item: any, index: any) => (
                            <div
                              key={index}
                              className={`  relative  left-[12px] ${
                                index == featureDataForSuperuser?.length - 1
                                  ? ''
                                  : 'border-white border-l-[0.1px] '
                              }`}
                            >
                              <div>
                                <div
                                  className={`relative xl:top-[13px] 2xl:top-[20px] h-[3vh] left-[-10px]  z-[-999]  `}
                                >
                                  <SideBarPointLine
                                    className={`w-8
                                    `}
                                  />
                                  {index ==
                                  featureDataForSuperuser?.length - 1 ? (
                                    <div className="border-white border-l-[0.1px] h-[7vh] relative left-[10px] xl:top-[-48px] 2xl:top-[-68px]"></div>
                                  ) : (
                                    ''
                                  )}
                                </div>
                                <Dropdown
                                  label=""
                                  placement="right-start"
                                  className={`z-50 text-white ${
                                    props?.open
                                      ? 'border-none bg-transparent border-none'
                                      : 'bg-[#1A252F] border-[#1A252F] '
                                  }   ${
                                    item?.feature == 'Production'
                                      ? ' transformDropdownProd w-fit'
                                      : 'transformDropdown'
                                  }  `}
                                  dismissOnClick={false}
                                  renderTrigger={() => (
                                    <div>
                                      <Link
                                        to={
                                          item?.disabled
                                            ? '#'
                                            : item?.feature == 'Location'
                                            ? `/app/Mines/${params?.mineId}/${item?.feature}/dashboard`
                                            : item?.feature == 'Forms'
                                            ? `/app/Mines/${params?.mineId}/${item?.feature}/New`
                                            : item?.feature == 'Production'
                                            ? `/app/Mines/${params?.mineId}/${item?.feature}/live/mine`
                                            : item?.feature == 'Setting'
                                            ? `/app/Mines/${params?.mineId}/${item?.feature}/users`
                                            : `/app/Mines/${params?.mineId}/${item?.feature}`
                                        }
                                        className="cursor-pointer"
                                      >
                                        <Tooltip
                                          content={item?.feature}
                                          placement="right"
                                          arrow={true}
                                          className={
                                            props?.open
                                              ? 'bg-transparent text-transparent'
                                              : 'bg-black text-white'
                                          }
                                        >
                                          <div
                                            className={`${
                                              params['*']?.includes(
                                                item?.feature
                                              )
                                                ? `flex bg-white text-black font-normal prodBeforeLine border border-white rounded-lg px-2 py-2  text-gray-600 ml-6 mr-3 ${
                                                    props?.open
                                                      ? 'w-[151px]'
                                                      : ''
                                                  } `
                                                : item?.disabled
                                                ? `flex font-medium  rounded-lg px-2 py-2 text-gray-500 cursor-not-allowed ml-6 mr-3`
                                                : `flex font-medium  rounded-lg px-2 py-2   ml-6 mr-3 `
                                            }
                                            `}
                                          >
                                            {item?.logo}
                                            {props.open ? (
                                              <span className="px-2">
                                                {item?.feature == 'Setting'
                                                  ? 'Settings'
                                                  : item?.feature == 'Location'
                                                  ? 'Locations'
                                                  : item?.feature}
                                              </span>
                                            ) : (
                                              ''
                                            )}
                                          </div>
                                        </Tooltip>
                                      </Link>
                                      {props?.open &&
                                      params[`*`]?.includes(item?.feature)
                                        ? item?.childerns?.map(
                                            (element: any, index: any) => (
                                              <div
                                                key={index}
                                                className="py-1 pt-2 ml-8">
                                                <div className="">
                                                  <Link
                                                    id="Dashboard"
                                                    to={`/app/Mines/${
                                                      params?.mineId
                                                    }/${item?.feature}/${
                                                      element?.value
                                                    }/${
                                                      element?.subTab
                                                        ? element?.subTab
                                                        : ''
                                                    }`}
                                                    className={
                                                      params['*']?.includes(
                                                        `${item?.feature}/${element?.value}`
                                                      )
                                                        ? `font-normal underline cursor-pointer text-[#FFB132] rounded-lg px-3 py-2 text-[16px] ml-2   ${
                                                            props?.open
                                                              ? 'w-[151px]'
                                                              : ''
                                                          }`
                                                        : ` text-white font-normal  rounded-lg px-3 py-2 cursor-pointer ml-2  text-[16px] `
                                                    }
                                                  >
                                                    {element?.title}
                                                  </Link>
                                                </div>
                                              </div>
                                            )
                                          )
                                        : ''}
                                    </div>
                                  )}
                                >
                                  {!props?.open &&
                                    item?.childerns?.map(
                                      (element: any, index: any) => (
                                        <div
                                          key={index}>
                                          <Dropdown.Item className="hover:bg-transparent">
                                            <div className="">
                                              <Link
                                                id="Dashboard"
                                                to={`/app/Mines/${
                                                  params?.mineId
                                                }/${item?.feature}/${
                                                  element?.value
                                                }/${
                                                  element?.subTab
                                                    ? element?.subTab
                                                    : ''
                                                }`}
                                                className={
                                                  params['*']?.includes(
                                                    `${item?.feature}/${element?.value}`
                                                  )
                                                    ? `font-normal underline cursor-pointer text-[#FFB132] rounded-lg px-3 py-2 text-[16px] ml-2   ${
                                                        props?.open
                                                          ? 'w-[151px]'
                                                          : ''
                                                      }`
                                                    : ` text-white font-normal  rounded-lg px-3 py-2 cursor-pointer ml-2  text-[16px] `
                                                }
                                              >
                                                {element?.title}
                                              </Link>
                                            </div>
                                          </Dropdown.Item>
                                        </div>
                                      )
                                    )}
                                </Dropdown>
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    )}
                  </div>
                )}
              >
                {!props?.open && mineDetails?.data?.name && (
                  <div>
                    <Dropdown.Item className="hover:bg-transparent w-[200px]">
                      <div className="">
                        <Link
                          id="Dashboard"
                          to={`#`}
                          className={`font-normal underline cursor-pointer text-[#FFB132] rounded-lg px-3 py-2 text-[16px] ml-2   ${
                            props?.open ? 'w-[151px]' : ''
                          }`}
                          onClick={() => props?.setOpen(!props?.open)}
                        >
                          {mineDetails?.data?.name}
                        </Link>
                      </div>
                    </Dropdown.Item>
                  </div>
                )}
              </Dropdown>

              <div className="py-1.5">
                <Link to={'/app/AuditLogs'}>
                  <Tooltip
                    content={props?.open ? '' : 'Audit Logs'}
                    placement="right"
                    arrow={true}
                    className={
                      props?.open ? 'bg-transparent' : 'bg-black text-white '
                    }
                  >
                    <div
                      id="setting_button"
                      className={
                        params[`*`]?.includes('AuditLogs')
                          ? `flex bg-white  text-black font-normal border border-white rounded-lg px-2 py-2  mx-3 ${
                              props?.open ? 'w-[151px]' : ''
                            } `
                          : ` flex items-center  font-normal rounded-lg px-2 py-2 text-[16px] cursor-pointer mx-3`
                      }
                    >
                      <div className={`flex ${props?.open ? '' : ''}`}>
                        <div className={`flex ${props?.open ? 'pt-1' : 'pl-2'}`}>
                          <AuditLogIcon
                            className={`h-6 w-4 ${
                              params['*']?.includes('AuditLogs')
                                ? 'text-black '
                                : ''
                            } ${props?.open ? '' : ' '}`}
                          />
                        </div>

                        {props.open ? (
                          <span className="px-2">Audit Logs</span>
                        ) : (
                          ''
                        )}
                      </div>
                    </div>
                  </Tooltip>
                </Link>
              </div>
            </div>
          ) : (
            <div
              className="my-1"
              style={{ position: 'relative', zIndex: 1000 }}
            >
              {featureData?.map((item: any, index: any) => (
                <div
                  key={index}>
                  <div className="py-1.5">
                    <Dropdown
                      label=""
                      placement="right-start"
                      className={`z-50 text-white ${
                        props?.open
                          ? 'border-none bg-transparent border-none'
                          : 'bg-[#1A252F] border-[#1A252F] '
                      }   ${
                        item?.feature == 'Production'
                          ? ' transformDropdownProd w-fit'
                          : item?.feature == 'Setting'
                          ? 'transformDropdownSetting'
                          : 'transformDropdown'
                      } `}
                      dismissOnClick={false}
                      renderTrigger={() => (
                        <div>
                          <Link
                            to={
                              item?.disabled
                                ? '#'
                                : item?.feature == 'Location'
                                ? `/app/${item?.feature}/dashboard`
                                : item?.feature == 'Forms'
                                ? `/app/${item?.feature}/Forms/New`
                                : item?.feature == 'Production'
                                ? `/app/${item?.feature}`
                                : item?.feature == 'Setting'
                                ? `/app/${item?.feature}/${settingChildernForUsers?.childerns[0]?.value}`
                                : `/app/${item?.feature}`
                            }
                          >
                            <Tooltip
                              content={item?.feature}
                              placement="right"
                              arrow={true}
                              className={
                                props?.open
                                  ? 'bg-transparent text-transparent'
                                  : 'bg-black text-white'
                              }
                            >
                              <div
                                className={
                                  params['*']?.includes(item?.feature)
                                    ? `flex bg-white text-black font-normal border border-white rounded-lg px-2 py-2  text-gray-600  mx-3 ${
                                        props?.open ? 'w-[151px]' : ''
                                      }`
                                    : item?.disabled
                                    ? `flex font-medium  rounded-lg px-2 py-2 text-gray-500 cursor-not-allowed  mx-3`
                                    : `flex font-medium  rounded-lg px-2 py-2   mx-3`
                                }
                              >
                                {item?.logo}
                                {props.open ? (
                                  <span className="px-2">
                                    {item?.feature == 'Setting'
                                      ? 'Settings'
                                      : item?.feature == 'Location'
                                      ? 'Locations'
                                      : item?.feature}
                                  </span>
                                ) : (
                                  ''
                                )}
                              </div>
                            </Tooltip>
                          </Link>
                          {props?.open && params[`*`]?.includes(item?.feature)
                            ? item?.childerns?.map(
                                (element: any, index: any) => (
                                  <div
                                    key={index}
                                    className="py-1 pt-2 ml-8">
                                    <div className="">
                                      <Link
                                        id="Dashboard"
                                        to={`/app/${item?.feature}/${
                                          element?.value
                                        }/${
                                          element?.subTab ? element?.subTab : ''
                                        }`}
                                        className={
                                          params['*']?.includes(
                                            `${item?.feature}/${element?.value}`
                                          )
                                            ? `font-normal underline cursor-pointer text-[#FFB132] rounded-lg px-3 py-2 text-[16px] ml-2   ${
                                                props?.open ? 'w-[151px]' : ''
                                              }`
                                            : ` text-white font-normal  rounded-lg px-3 py-2 cursor-pointer ml-2  text-[16px] `
                                        }
                                      >
                                        {element?.title}
                                      </Link>
                                    </div>
                                  </div>
                                )
                              )
                            : ''}
                        </div>
                      )}
                    >
                      {!props?.open &&
                        item?.childerns?.map((element: any, index: any) => (
                          <div
                            key={index}>
                            <Dropdown.Item className="hover:bg-transparent">
                              <div className="">
                                <Link
                                  id="Dashboard"
                                  to={`/app/${item?.feature}/${
                                    element?.value
                                  }/${element?.subTab ? element?.subTab : ''}`}
                                  className={
                                    params['*']?.includes(
                                      `${item?.feature}/${element?.value}`
                                    )
                                      ? `font-normal underline cursor-pointer text-[#FFB132] rounded-lg px-3 py-2 text-[16px] ml-2   ${
                                          props?.open ? 'w-[151px]' : ''
                                        }`
                                      : ` text-white font-normal  rounded-lg px-3 py-2 cursor-pointer ml-2  text-[16px] `
                                  }
                                >
                                  {element?.title}
                                </Link>
                              </div>
                            </Dropdown.Item>
                          </div>
                        ))}
                    </Dropdown>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        {isTabletMid ? (
          ''
        ) : (
          <div className={`mb-2  ${props?.open ? 'w-[220px]' : ''}`}>
            <div
              className={`float-end  ${props?.open ? '' : 'mr-[5px]'}`}
              onClick={() => props?.setOpen(!props?.open)}
            >
              <LeftArrow
                className={` cursor-pointer w-6 h-6 ${
                  props?.open ? '' : 'rotate-180 mr-[1.625rem] '
                }`}
              />
            </div>
          </div>
          )}
        </motion.div>
    </div>
  );
};

export default Sidebar;
