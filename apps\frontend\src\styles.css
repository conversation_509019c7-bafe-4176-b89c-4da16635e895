@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Poppins:wght@500&display=swap');
@import url('https://fonts.adobe.com/fonts/proxima-nova#details-section+proxima-nova-regular');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;

    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --ring: 216 34% 17%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}
.autoComOff:-webkit-autofill,
.autoComOff:-webkit-autofill:hover,
.autoComOff:-webkit-autofill:focus,
.autoComOff:-webkit-autofill:active {
  -webkit-text-fill-color: white !important;
  caret-color: white;
  transition: background-color 5000s ease-in-out 0s;
}

select > option:not(:first-child) {
  color: #000;
}
select > option:first-hand {
  color: #ccc;
}

.Toastify__toast.Toastify__toast-theme--light.Toastify__toast--success,
.Toastify__toast.Toastify__toast-theme--light.Toastify__toast--info,
.Toastify__toast.Toastify__toast-theme--light.Toastify__toast--warning,
.Toastify__toast.Toastify__toast-theme--light.Toastify__toast--error {
  @apply bg-[#1D2833];
  @apply text-white;
  @apply h-[56px];
}

.Toastify__close-button,
.Toastify__close-button--light {
  color: white !important;
  opacity: 1 !important;
  margin-top: 10px;
  background-color: #2b3c4d !important;
  width: 23px !important;
  height: 24px;
  padding: 5px !important;
  /* text-align: center; */
  margin: auto !important;
  border-radius: 50% !important;
}
body {
  color: rgb(var(--foreground-rgb));
  font-family: Inter, sans-serif;
  /* background: ;\ */
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Old versions of Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
}
.text-provima {
  font-family: proxima-nova, sans-serif;
}
*::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.toggleShadowActive {
  box-shadow: 0px 0px 3px 1px #96fb6094;
}

.toggleShadowInActive {
  box-shadow: 0px 0px 3px 1px gray;
}

.bg-hero {
  background-image: url('./assets/bg.jpg');
  background-repeat: no-repeat;
  background-size: cover;
}
.float-end {
  float: inline-end;
}

.forgotPas:focus-visible {
  outline: auto;
  padding-top: 2px;
  padding-bottom: 2px;
  width: fit-content;
}

*::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 5px;
}

*::-webkit-scrollbar-thumb {
  background-color: #ebebeb;
  border-radius: 14px;
  border: 3px solid #ebebeb;
}
.box {
  background: linear-gradient(
      0deg,
      rgba(51, 94, 157, 0.2),
      rgba(51, 94, 157, 0.2)
    ),
    linear-gradient(
      59.52deg,
      rgba(74, 168, 254, 0.34) 11.25%,
      rgba(74, 168, 254, 0) 58.65%
    ),
    radial-gradient(
      40.5% 111.43% at 86.27% 100%,
      rgba(46, 205, 255, 0.2) 0%,
      rgba(18, 26, 33, 0) 100%
    ),
    radial-gradient(
      67.21% 122.8% at 66.95% 18.69%,
      rgba(74, 168, 254, 0.43) 0%,
      rgba(18, 26, 33, 0) 100%
    ),
    linear-gradient(107.36deg, #121a21 5.24%, #19344b 100%);
  background-blend-mode: color, normal, normal, normal, normal;
}

.box.arrow-top {
  margin-top: 40px;
}

.box.arrow-top:after {
  content: ' ';
  position: absolute;
  right: 10px;
  top: -15px;
  border-top: none;
  border-right: 15px solid transparent;
  border-left: 15px solid transparent;
  border-bottom: 15px solid white;
}

.mainLogo {
  animation: mymove 5s infinite;
  /* width: 22rem; */
  /* height: 21rem; */
  /* top: 0px; */
  position: relative;
  top: -157px;
  z-index: 1;
}

.wdrag,
.edrag {
  display: none;
}
.transformDropdownSetting {
  transform: translate(92px, 186px) !important;
}
.transformDropdown {
  transform: translate(91px, 58px) !important;
}
.transformDropdownProd {
  transform: translate(91px, 111px) !important;
}

.tableheightForlg {
  height: calc(100vh - 22rem);
}
.minerNameHide {
  display: none;
}

.tableheightFormd {
  height: calc(100vh - 22rem);
}
.tableBGPersonnel {
  background-color: rgb(37, 53, 67, 33%);
}
.userMTableBg {
  background: rgb(45, 92, 136);
  background: linear-gradient(
    90deg,
    rgba(45, 92, 136, 0.804359243697479) 0%,
    rgba(33, 73, 110, 1) 55%
  );
}
.prodGoalsTableBg {
  background: rgb(45, 92, 136);
  background: linear-gradient(
    90deg,
    rgba(45, 92, 136, 0.804359243697479) 0%,
    rgba(33, 73, 110, 1) 55%
  );
}
@keyframes mymove {
  0% {
    opacity: 1;
  }
  15% {
    opacity: 1;
  }
  25% {
    opacity: 1;
  }
  35% {
    opacity: 0;
  }
  55% {
    opacity: 0;
  }
  75% {
    opacity: 0;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 1;
  }
}

.tableTitle {
  background-color: rgba(37, 53, 67, 0.33);
}

.shadow-section {
  --section-box-shadow: 0px 0px 60px 0px #4aa8fe, 0px 7px 9px 0px #14324c8a;
  box-shadow: var(--section-box-shadow);
}

.table-container {
  max-height: '56px';
  overflow-y: auto;
}
.cursor-ew-resize {
  cursor: crosshair !important;
}

.agBreakup {
  background: linear-gradient(0deg, #2061a4 0%, rgba(44, 91, 139, 1) 100%);
}

.agBreakup2 {
  background: linear-gradient(0deg, rgba(44, 91, 139, 1) 0%, #2061a4 100%);
}

.agBreakupTable {
  background: linear-gradient(181.88deg, #20466e 1.59%, #2160a0 98.46%);
}
.locationDashboardAG {
  background: rgb(255, 255, 255);
  background: linear-gradient(
    0deg,
    rgba(255, 255, 255, 0.2189250700280112) 5%,
    rgba(12, 75, 142, 1) 100%
  );
}

.activityColor {
  background: rgba(37, 53, 67, 0.32);
}

.insightsColor {
  background: rgba(0, 0, 0, 0.16);
}

.crisp {
  stroke: rgba(74, 168, 256, 50%) !important;
}

.w-\[177px\] {
  width: 177px;
}

.w-\[454px\] {
  width: 454px;
}

.w-\[554px\] {
  width: 554px;
}
.ticks {
  display: none;
}

.filledAreaColor {
  background: linear-gradient(
    /* 0deg, */ rgba(56, 134, 206, 0.44) rgba(35, 70, 101, 0)
  );
}

.unselectable {
  user-select: none;
}

.js-plotly-plot .plotly .cursor-crosshair {
  cursor: auto !important;
}

.custom-datepicker
  .absolute.right-0.h-full.px-3.text-gray-400.focus\:outline-none.disabled\:opacity-40.disabled\:cursor-not-allowed {
  display: none !important;
}
/* Hide the clear (X) icon based on SVG path */
.custom-datepicker svg path[d="M6 18L18 6M6 6l12 12"] {
  display: none;
}



.center-text {
  text-align: center;
}

.grid-container {
  height: calc(100vh - 200px);
  grid-template-columns: 260px auto 260px;
}

.grid-container-wide {
  height: calc(100vh - 200px);
  grid-template-columns: auto;
}

.grid-item {
  background: #446282;
  border: 1px dashed #ffffff;
}

.grid-item-preview {
  background: #446282;
  border: 1px dashed #ffffff;
}

.form-designer-tool {
  background: #ffffff22;
  padding: 0.5rem;
  border: 1px dashed #ffffff;
  pointer-events: none;
}

.drag-overlay {
  width: 150px;
  height: 30px;
  background: #ffffff22;
  border: 1px solid #fff092;
}

.drag-highlight {
  position: absolute;
  width: 20px;
  height: 20px;
  background: #ffffff22;
}

.top-16 {
  top: 4rem;
}

.form-input-type {
  border: 1px dashed #ffffff00;
}

.form-input-type.isSelected,
.form-input-type:hover {
  cursor: pointer;
  border-color: #ffffff;
}

.form-input-type *:hover {
  cursor: pointer;
}

.rotate-135 {
  rotate: 135deg;
}

.rotate-225 {
  rotate: 225deg;
}

.rotate-270 {
  rotate: 270deg;
}

.rotate-315 {
  rotate: 315deg;
}

.droppable-highlight {
  box-shadow: inset 0px 0px 2px 4px #0ea5e9;
}

input, select {
  height: 32px;
}

.basis-50 {
  flex-basis: 50%;
}

.self-end {
  align-self: end;
}

.button-active {
  border: 1px solid #ffffff;
  border-radius: 4px;
  background-color: #00000033;
}

.template-group {
  border: 1px dashed #ffffff;
  width: 100%;
}

.template-group-preview {
  width: 100%;
}

.template-group-empty {
  padding: 1rem;
  border: 1px solid #0ea5e9;
  width: 100%;
}

.form-item {
  border: 1px dashed #ffffff;
}

.group-label {
  top: -11px;
  left: 11px;
  background: #446282;
  padding: 1px 4px;
  font-size: 12px;
}

.group-dl-btn,
.group-sl-btn,
.group-sr-btn,
.group-dr-btn {
  background: #446282;
  padding: 1px;
  font-size: 14px;
}
.group-dl-btn {
  top: -9px;
  left: 70px;
}
.group-sl-btn {
  top: -9px;
  left: 88px;
}
.group-sr-btn {
  top: -9px;
  left: 106px;
}
.group-dr-btn {
  top: -9px;
  left: 124px;
}

.group-del-btn {
  top: -11px;
  right: 11px;
  background: #446282;
  padding: 1px;
  font-size: 14px;
}

.group-clone-btn {
  top: -11px;
  right: 38px;
  background: #446282;
  padding: 1px;
  font-size: 14px;
}

.item-label {
  color: #ffffff;
  background-color: #ffffff22;
  border-radius: 2px;
  padding: 1px 4px;
  font-size: 12px;
}

.template-add-group {
  padding: 1rem;
  width: 100%;
}

.selected {
  border: 1px solid #4AA8FE;
}

.form-question {

}

.padded-form-question {
  padding-top: 2.1rem;
}
.form-check-input[type="radio"] {
  width: 1.5em;
  height: 1.5em;
}
.form-check-input[type="radio"]:checked {
  background-color: #FFB132;
  border-color: #ffffff;
  border-width: 4px;
}

.form-check-input[type="checkbox"] {
  width: 1.5em;
  height: 1.5em;
}
.form-check-input[type="checkbox"]:checked {
  background-color: #FFB132;
  border-color: #ffffff;
  border-width: 4px;
}

.basis-50 {
  flex-basis: 50%;
}

.basis-100 {
  flex-basis: 100%;
}

.gap-x-0 {
  column-gap: 0px;
}

.gap-x-1 {
  column-gap: 0.25rem;
}

.gap-x-2 {
  column-gap: 0.5rem;
}

.gap-x-3 {
  column-gap: 0.75rem;
}

.gap-x-4 {
  column-gap: 1rem;
}

.color-property, .color-element {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 64px;
  height: 28px;
  background-color: transparent;
  cursor: pointer;
}

input[type="color"].color-property::-webkit-color-swatch,
input[type="color"].color-element::-webkit-color-swatch {
  border-radius: 8px !important;
  border: 2px solid #ffffff;
}


input[type="color"].color-property::-moz-color-swatch,
input[type="color"].color-element::-moz-color-swatch {
  border-radius: 8px !important;
  border: 2px solid #ffffff;
}