import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Req,
} from '@nestjs/common';
import { FormTemplateDefinitionsService } from './form_template_definitions.service';
import { CreateFormTemplateDefinitionDto } from './dto/create-form-template-definitions.dto';
import { UpdateFormTemplateDefinitionDto } from './dto/update-form-template-definitions.dto';
import { ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';
import { Roles } from '../auth/gurads/roles.decorator';
import { RolesGuard } from '../auth/gurads/roles.guard';

@Controller('portal/v1/formTemplateDefinitions')
@ApiTags('formTemplateDefinitions')
export class FormTemplateDefinitionsController {
  constructor(private readonly formTemplateDefinitionsService: FormTemplateDefinitionsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(@Body() createFormTemplateDefinitionDto: CreateFormTemplateDefinitionDto) {
    return this.formTemplateDefinitionsService.create(createFormTemplateDefinitionDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(@Request() req) {
    let mineId = req.user.mineid;
    return this.formTemplateDefinitionsService.findAll(mineId);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async findOne(@Param('id') id: number) {
    return this.formTemplateDefinitionsService.findOne(id);
  }

  @Post('revise/:id')
  @UseGuards(JwtAuthGuard)
  async revise(@Param('id') id: number, @Body() createFormTemplateDefinitionDto: CreateFormTemplateDefinitionDto, @Req() req) {
    return this.formTemplateDefinitionsService.revise(id, createFormTemplateDefinitionDto, req.user);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  async update(@Param('id') id: number, @Body() updateFormTemplateDefinitionDto: UpdateFormTemplateDefinitionDto, @Req() req) {
    return this.formTemplateDefinitionsService.update(id, updateFormTemplateDefinitionDto, req.user);
  }

  @Patch('publish/:id')
  @UseGuards(JwtAuthGuard)
  async publish(@Param('id') id: number, @Req() req) {
    return this.formTemplateDefinitionsService.publish(id, req.user);
  }

  @Patch('/isDelete/:id')
  @Roles(['admin', 'superuser'])
  @UseGuards(JwtAuthGuard, RolesGuard)
  async deleteDefinition(@Param('id') id: number) {
    let deletedDefinition = await this.formTemplateDefinitionsService.deleteDefinition(id);
    return deletedDefinition;
  }
}
