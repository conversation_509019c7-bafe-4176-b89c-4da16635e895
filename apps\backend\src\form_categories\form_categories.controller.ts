import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { FormCategoriesService } from './form_categories.service';
import { CreateFormCategoryDto } from './dto/create-form-category.dto';
import { ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/gurads/jwt.guard';

@Controller('portal/v1/formCategories')
@ApiTags('formCategories')
export class FormCategoriesController {
  constructor(private readonly formCategoryService: FormCategoriesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(@Body() formCategoryBody: CreateFormCategoryDto) {
    const formCategories = await this.formCategoryService.create(formCategoryBody);
    return formCategories;
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  find() {
    return this.formCategoryService.find();
  }

  @Get('/:id')
  @UseGuards(JwtAuthGuard)
  findOne(@Param('id') id: number) {
    return this.formCategoryService.findOne(id);
  }
}
